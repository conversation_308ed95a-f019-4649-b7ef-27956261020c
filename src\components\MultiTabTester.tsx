import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { ExternalLink, Users, Copy, RefreshCw, Layout } from 'lucide-react';

const MultiTabTester: React.FC = () => {
  const { user, signOut, signInAnonymously } = useAuth();
  const { toast } = useToast();
  const [gameId, setGameId] = useState('');
  const [tabId] = useState(() => Math.random().toString(36).substr(2, 9));

  // Store tab info in localStorage for cross-tab communication
  useEffect(() => {
    if (user) {
      const tabInfo = {
        tabId,
        userName: user.user_metadata?.name || 'Unknown',
        userId: user.id,
        timestamp: Date.now()
      };
      
      // Store this tab's info
      localStorage.setItem(`tab_${tabId}`, JSON.stringify(tabInfo));
      
      // Clean up old tab info (older than 5 minutes)
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('tab_')) {
          try {
            const info = JSON.parse(localStorage.getItem(key) || '{}');
            if (info.timestamp < fiveMinutesAgo) {
              localStorage.removeItem(key);
            }
          } catch (error) {
            localStorage.removeItem(key);
          }
        }
      });
    }
    
    return () => {
      // Clean up this tab's info when component unmounts
      localStorage.removeItem(`tab_${tabId}`);
    };
  }, [user, tabId]);

  const getActiveTabs = () => {
    const tabs: any[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('tab_')) {
        try {
          const info = JSON.parse(localStorage.getItem(key) || '{}');
          tabs.push(info);
        } catch (error) {
          // Ignore invalid tab info
        }
      }
    });
    return tabs.sort((a, b) => a.timestamp - b.timestamp);
  };

  const [activeTabs, setActiveTabs] = useState(getActiveTabs());

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTabs(getActiveTabs());
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const createUserInNewTab = async (userName: string) => {
    try {
      // Open new tab with specific user creation
      const url = new URL(window.location.origin);
      url.searchParams.set('createUser', userName);
      if (gameId) {
        url.searchParams.set('autoJoin', gameId);
      }

      const newTab = window.open(url.toString(), '_blank');

      if (newTab) {
        toast({
          title: "New Tab Opened",
          description: `Opening new tab to create user: ${userName}`,
        });
      } else {
        toast({
          title: "Pop-up Blocked",
          description: "Please allow pop-ups and try again",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error opening new tab:', error);
      toast({
        title: "Error",
        description: "Failed to open new tab. Try manual method.",
        variant: "destructive"
      });
    }
  };

  const handleQuickUserCreation = async (userName: string) => {
    try {
      await signOut();
      await new Promise(resolve => setTimeout(resolve, 500));
      await signInAnonymously(userName);
      
      toast({
        title: "User Created",
        description: `Now logged in as: ${userName}`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create user",
        variant: "destructive"
      });
    }
  };

  // Handle URL parameters for automatic user creation
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const createUser = urlParams.get('createUser');
    const autoJoin = urlParams.get('autoJoin');
    
    if (createUser && !user) {
      console.log('Auto-creating user from URL:', createUser);
      signInAnonymously(createUser).then(() => {
        if (autoJoin) {
          setGameId(autoJoin);
          toast({
            title: "Auto-Created User",
            description: `Created ${createUser}. You can now join game: ${autoJoin}`,
          });
        }
      });
      
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [user, signInAnonymously]);

  const copyGameId = () => {
    if (gameId) {
      navigator.clipboard.writeText(gameId);
      toast({
        title: "Copied!",
        description: `Game ID ${gameId} copied to clipboard`,
      });
    }
  };

  const joinGame = () => {
    if (gameId.trim()) {
      window.location.href = `/waiting/${gameId}`;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Layout className="w-5 h-5" />
          Multi-Tab Testing Helper
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Tab Info */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold mb-2">Current Tab</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm">
                <strong>User:</strong> {user ? (user.user_metadata?.name || 'Guest') : 'Not logged in'}
              </p>
              <p className="text-sm text-muted-foreground">
                <strong>Tab ID:</strong> {tabId}
              </p>
            </div>
            <Badge variant={user ? 'default' : 'secondary'}>
              {user ? 'Logged In' : 'No User'}
            </Badge>
          </div>
        </div>

        {/* Active Tabs Overview */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Active Tabs ({activeTabs.length})</h3>
            <Button size="sm" variant="outline" onClick={() => setActiveTabs(getActiveTabs())}>
              <RefreshCw className="w-4 h-4 mr-1" />
              Refresh
            </Button>
          </div>
          
          {activeTabs.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">No active tabs detected</p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {activeTabs.map((tab) => (
                <div
                  key={tab.tabId}
                  className={`p-3 rounded-lg border ${
                    tab.tabId === tabId ? 'bg-blue-50 border-blue-200' : 'bg-muted'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{tab.userName}</p>
                      <p className="text-xs text-muted-foreground">
                        {tab.tabId === tabId ? 'This Tab' : `Tab: ${tab.tabId.slice(0, 6)}`}
                      </p>
                    </div>
                    <Badge variant={tab.tabId === tabId ? 'default' : 'secondary'}>
                      {tab.tabId === tabId ? 'Current' : 'Other'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Quick User Creation for Current Tab */}
        <div className="space-y-3">
          <h3 className="font-semibold">Quick User Creation (Current Tab)</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button size="sm" variant="outline" onClick={() => handleQuickUserCreation('Host Player')}>
              Host Player
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleQuickUserCreation('Player 2')}>
              Player 2
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleQuickUserCreation('Player 3')}>
              Player 3
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleQuickUserCreation('Player 4')}>
              Player 4
            </Button>
          </div>
        </div>

        {/* New Tab Creation */}
        <div className="space-y-3">
          <h3 className="font-semibold">Create Users in New Tabs</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button size="sm" variant="outline" onClick={() => createUserInNewTab('Host Player')}>
              <ExternalLink className="w-3 h-3 mr-1" />
              Host Player
            </Button>
            <Button size="sm" variant="outline" onClick={() => createUserInNewTab('Player 2')}>
              <ExternalLink className="w-3 h-3 mr-1" />
              Player 2
            </Button>
            <Button size="sm" variant="outline" onClick={() => createUserInNewTab('Player 3')}>
              <ExternalLink className="w-3 h-3 mr-1" />
              Player 3
            </Button>
            <Button size="sm" variant="outline" onClick={() => createUserInNewTab('Player 4')}>
              <ExternalLink className="w-3 h-3 mr-1" />
              Player 4
            </Button>
          </div>
        </div>

        {/* Game ID Management */}
        <div className="space-y-3">
          <h3 className="font-semibold">Game ID for Testing</h3>
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="game-id">Game ID</Label>
              <Input
                id="game-id"
                placeholder="Enter or paste game ID"
                value={gameId}
                onChange={(e) => setGameId(e.target.value)}
              />
            </div>
            <div className="flex items-end gap-2">
              <Button size="sm" variant="outline" onClick={copyGameId} disabled={!gameId}>
                <Copy className="w-4 h-4 mr-1" />
                Copy
              </Button>
              <Button size="sm" onClick={joinGame} disabled={!gameId || !user}>
                Join Game
              </Button>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-semibold mb-2">Multi-Tab Testing Instructions:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li><strong>Tab 1:</strong> Create "Host Player" → Create mock game → Copy game ID</li>
            <li><strong>Click "Player 2" with external link icon</strong> → Opens new tab with Player 2</li>
            <li><strong>Click "Player 3" with external link icon</strong> → Opens new tab with Player 3</li>
            <li><strong>In new tabs:</strong> Paste game ID and join game</li>
            <li><strong>All tabs:</strong> Mark ready → Host starts game</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default MultiTabTester;
