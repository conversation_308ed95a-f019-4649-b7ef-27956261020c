import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const { gameId } = await req.json();

    // Get game and players
    const { data: game, error: gameError } = await supabase
      .from('games')
      .select('*')
      .eq('id', gameId)
      .single();

    if (gameError) throw gameError;

    const { data: players, error: playersError } = await supabase
      .from('players')
      .select('*')
      .eq('game_id', gameId)
      .eq('is_ready', true);

    if (playersError) throw playersError;

    if (players.length < 3) {
      throw new Error('Need at least 3 players to start');
    }

    // Shuffle players for random role assignment
    const shuffledPlayers = [...players].sort(() => Math.random() - 0.5);

    // Assign roles - simplified to just mafia and civilian for now
    const roleAssignments = [];
    let mafiaAssigned = 0;

    for (let i = 0; i < shuffledPlayers.length; i++) {
      let role = 'civilian';

      if (mafiaAssigned < game.mafia_count) {
        role = 'mafia';
        mafiaAssigned++;
      }

      roleAssignments.push({
        id: shuffledPlayers[i].id,
        role: role
      });
    }

    // Update players with roles
    for (const assignment of roleAssignments) {
      await supabase
        .from('players')
        .update({ role: assignment.role })
        .eq('id', assignment.id);
    }

    // Update game status to mafia_reveal first
    await supabase
      .from('games')
      .update({
        status: 'mafia_reveal',
        current_phase_end: new Date(Date.now() + 5000).toISOString() // 5 seconds for mafia reveal
      })
      .eq('id', gameId);

    return new Response(
      JSON.stringify({ success: true, message: 'Roles assigned successfully' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error assigning roles:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});