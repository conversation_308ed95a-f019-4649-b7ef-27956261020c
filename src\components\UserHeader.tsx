import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { LogOut, User } from 'lucide-react';

const UserHeader: React.FC = () => {
  const { user, signOut } = useAuth();
  const { toast } = useToast();

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Logged Out",
        description: "You have been logged out successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to log out",
        variant: "destructive"
      });
    }
  };

  if (!user) return null;

  return (
    <div className="w-full bg-muted/50 border-b border-border">
      <div className="max-w-7xl mx-auto px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <User className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">
              {user.user_metadata?.name || user.email || 'Guest User'}
            </span>
            <Badge variant={user.id.startsWith('guest_') ? 'secondary' : 'default'} className="text-xs">
              {user.id.startsWith('guest_') ? 'Guest' : 'Auth'}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground hidden md:block">
              ID: {user.id.slice(-8)}
            </span>
            <Button size="sm" variant="ghost" onClick={handleLogout}>
              <LogOut className="w-3 h-3 mr-1" />
              Logout
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserHeader;
