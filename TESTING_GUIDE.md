# 🧪 Mafia Video Chat Game - Comprehensive Testing Guide

## 🎯 **Testing Overview**

This guide provides step-by-step instructions for testing all functionality of the Mafia video chat game from start to finish.

## 📋 **Pre-Testing Checklist**

- [ ] Development server is running on <http://localhost:8080>
- [ ] <PERSON><PERSON><PERSON> has camera/microphone permissions enabled
- [ ] Multiple browser windows/tabs available for multi-player testing
- [ ] Browser Developer Tools open (F12) for monitoring console/network

## 🔍 **Phase 1: Authentication & Database Testing**

### Step 1: Authentication Flow

1. **Open** <http://localhost:8080>
2. **Verify** authentication screen appears
3. **Test Guest Authentication:**
   - Enter name: "TestPlayer1"
   - Click "Play as Guest"
   - **Expected:** Redirect to main game interface
   - **Check:** Console shows authentication success logs

### Step 2: Database Connection Test

1. **Locate** "Database Connection Test" component on main page
2. **Click** "Run Database Tests"
3. **Expected Results:**
   - ✅ Database connection successful
   - ✅ Table structure is correct
   - ✅ Game creation successful
   - ✅ Player creation successful
   - ✅ Cleanup completed

### Step 3: Simple Game Creation Test

1. **Locate** "Simple Game Creator" component
2. **Enter** game name: "Test Game 1"
3. **Click** "Create Simple Game"
4. **Expected:**
   - Success toast appears
   - Navigation to waiting room
   - Game ID displayed

## 🎮 **Phase 2: Complete Mock Game Flow Testing**

### Step 4: Multi-Player Setup (ENHANCED)

1. **Create mock game** using Mock Game Creator
2. **Use Multi-Player Testing Helper:**
   - Click "Copy ID" to copy game ID
   - Click "Open in New Tab" to open additional browser windows
3. **In each new window:**
   - Navigate to <http://localhost:8080>
   - Authenticate as guest with different names ("Player2", "Player3")
   - Use "Join Game" and enter the copied game ID
4. **Verify** Multi-Player Testing Helper shows all players

### Step 5: Waiting Room Verification (ENHANCED)

1. **Verify** all 3 players appear in waiting room
2. **Check** "MOCK" badge appears next to game ID
3. **Check** host has crown icon
4. **Test** ready functionality:
   - Each player clicks their ready button
   - Ready status updates in real-time
   - Start button becomes enabled when 3+ ready
5. **Host clicks** "Start Game"

### Step 6: Game Phase Testing

#### Mafia Reveal Phase (5 seconds)

1. **Verify** timer shows 5 seconds
2. **Check** role-specific messages:
   - Mafia players see identification message
   - Civilians see "cannot see/hear" message
3. **Wait** for automatic phase transition

#### Discussion Phase

1. **Verify** timer shows configured discussion time
2. **Test** video chat functionality:
   - All players can see each other
   - Camera/mic controls work
   - Audio/video quality is acceptable
3. **Test** chat functionality (if implemented)

#### Voting Phase

1. **Verify** voting interface appears
2. **Test** voting mechanics:
   - Can select target player
   - Cannot vote for self
   - Vote submission works
3. **Wait** for vote results and elimination

#### Elimination & Observer Mode

1. **Verify** eliminated player's role is revealed
2. **Check** eliminated player enters observer mode:
   - Can see other players
   - Cannot be seen by living players
   - Cannot participate in voting

### Step 7: Win Condition Testing

1. **Continue** game phases until win condition met
2. **Test** both scenarios:
   - Civilians eliminate all mafia (civilian victory)
   - Mafia equals/outnumbers civilians (mafia victory)
3. **Verify** final discussion phase (1 minute)
4. **Check** game end screen with statistics

## 🔧 **Phase 3: Error Handling & Edge Cases**

### Step 8: Error Scenarios

1. **Test** invalid game creation:
   - Empty game name
   - Invalid player counts
   - Invalid mafia counts
2. **Test** network issues:
   - Disconnect during game
   - Refresh browser during game
3. **Test** permission issues:
   - Deny camera/microphone access
   - Verify graceful fallback

### Step 9: Browser Compatibility

1. **Test** in multiple browsers:
   - Chrome (recommended)
   - Firefox
   - Edge
   - Safari (if available)
2. **Verify** WebRTC functionality in each

## 📊 **Phase 4: Performance & UI Testing**

### Step 10: Performance Verification

1. **Monitor** browser performance during gameplay
2. **Check** for memory leaks during long sessions
3. **Verify** smooth video chat with multiple participants

### Step 11: UI/UX Testing

1. **Test** responsive design on different screen sizes
2. **Verify** all animations and transitions work smoothly
3. **Check** accessibility features

## ✅ **Success Criteria**

### Core Functionality

- [ ] Authentication works (guest and registered users)
- [ ] Database operations succeed (create/read/update)
- [ ] Game creation and joining works
- [ ] Video chat functions properly
- [ ] All game phases transition correctly
- [ ] Win conditions trigger properly
- [ ] Observer mode works for eliminated players

### Advanced Features

- [ ] Public/private game settings work
- [ ] Timer management functions correctly
- [ ] Role-based visibility controls work
- [ ] Final discussion phase includes all players
- [ ] Game end screen shows correct statistics

### Error Handling

- [ ] Proper error messages for invalid inputs
- [ ] Graceful handling of network issues
- [ ] Fallback for camera/microphone permission denials

## 🐛 **Common Issues & Solutions**

### Database Connection Issues

- **Problem:** "Database connection failed"
- **Solution:** Check Supabase configuration and RLS policies

### Authentication Issues

- **Problem:** Stuck on authentication screen
- **Solution:** Check browser console for errors, try guest authentication

### Video Chat Issues

- **Problem:** Camera/microphone not working
- **Solution:** Check browser permissions, try different browser

### Game Creation Issues

- **Problem:** "Failed to create game"
- **Solution:** Check database permissions, verify user authentication

## 📝 **Test Results Template**

```text
Date: ___________
Tester: ___________
Browser: ___________

Phase 1 - Authentication & Database:
[ ] Authentication works
[ ] Database tests pass
[ ] Simple game creation works

Phase 2 - Game Flow:
[ ] Multi-player setup works
[ ] Waiting room functions
[ ] Mafia reveal phase works
[ ] Discussion phase works
[ ] Voting phase works
[ ] Elimination works
[ ] Win conditions work

Phase 3 - Error Handling:
[ ] Invalid inputs handled
[ ] Network issues handled
[ ] Permission issues handled

Phase 4 - Performance & UI:
[ ] Performance acceptable
[ ] UI responsive
[ ] Animations smooth

Overall Result: PASS / FAIL
Notes: ___________
```
