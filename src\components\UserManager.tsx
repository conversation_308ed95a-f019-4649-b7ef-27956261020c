import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { LogOut, UserPlus, Users, RefreshCw } from 'lucide-react';

const UserManager: React.FC = () => {
  const { user, signOut, signInAnonymously } = useAuth();
  const { toast } = useToast();
  const [newUserName, setNewUserName] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Logged Out",
        description: "You have been logged out successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to log out",
        variant: "destructive"
      });
    }
  };

  const handleCreateNewUser = async () => {
    if (!newUserName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a name for the new user",
        variant: "destructive"
      });
      return;
    }

    setIsCreating(true);
    try {
      // First logout current user
      await signOut();
      
      // Small delay to ensure logout is complete
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Create new user
      await signInAnonymously(newUserName.trim());
      
      toast({
        title: "New User Created",
        description: `Successfully created user: ${newUserName}`,
      });
      
      setNewUserName('');
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create new user",
        variant: "destructive"
      });
    } finally {
      setIsCreating(false);
    }
  };

  const quickCreateUser = async (name: string) => {
    setIsCreating(true);
    try {
      await signOut();
      await new Promise(resolve => setTimeout(resolve, 300));
      await signInAnonymously(name);
      
      toast({
        title: "Switched User",
        description: `Now logged in as: ${name}`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to switch user",
        variant: "destructive"
      });
    } finally {
      setIsCreating(false);
    }
  };

  const refreshUser = () => {
    window.location.reload();
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5" />
          User Manager - Multi-Player Testing
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current User Info */}
        {user ? (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h3 className="font-semibold">Current User</h3>
                <p className="text-sm text-muted-foreground">
                  <strong>Name:</strong> {user.user_metadata?.name || user.email || 'Guest'}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>ID:</strong> {user.id}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={user.id.startsWith('guest_') ? 'secondary' : 'default'}>
                  {user.id.startsWith('guest_') ? 'Guest User' : 'Authenticated User'}
                </Badge>
                <Button size="sm" variant="outline" onClick={handleLogout}>
                  <LogOut className="w-4 h-4 mr-1" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
            <p className="text-muted-foreground">No user currently logged in</p>
            <Button className="mt-2" onClick={refreshUser}>
              <RefreshCw className="w-4 h-4 mr-1" />
              Refresh Page
            </Button>
          </div>
        )}

        {/* Create New User */}
        <div className="space-y-3">
          <h3 className="font-semibold">Create New User</h3>
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="new-user-name">User Name</Label>
              <Input
                id="new-user-name"
                placeholder="Enter name for new user"
                value={newUserName}
                onChange={(e) => setNewUserName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleCreateNewUser()}
              />
            </div>
            <div className="flex items-end">
              <Button 
                onClick={handleCreateNewUser}
                disabled={!newUserName.trim() || isCreating}
              >
                <UserPlus className="w-4 h-4 mr-1" />
                {isCreating ? 'Creating...' : 'Create User'}
              </Button>
            </div>
          </div>
        </div>

        {/* Quick User Templates */}
        <div className="space-y-3">
          <h3 className="font-semibold">Quick User Templates</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => quickCreateUser('Host Player')}
              disabled={isCreating}
            >
              Host Player
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => quickCreateUser('Player 2')}
              disabled={isCreating}
            >
              Player 2
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => quickCreateUser('Player 3')}
              disabled={isCreating}
            >
              Player 3
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => quickCreateUser('Player 4')}
              disabled={isCreating}
            >
              Player 4
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => quickCreateUser('Tester')}
              disabled={isCreating}
            >
              Tester
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => quickCreateUser('Observer')}
              disabled={isCreating}
            >
              Observer
            </Button>
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-semibold mb-2">Multi-Player Testing Instructions:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li><strong>Tab 1:</strong> Create "Host Player" → Create mock game</li>
            <li><strong>Tab 2:</strong> Create "Player 2" → Join game with ID</li>
            <li><strong>Tab 3:</strong> Create "Player 3" → Join game with ID</li>
            <li><strong>All tabs:</strong> Mark ready → Host starts game</li>
            <li><strong>Test:</strong> Complete game flow with different roles</li>
          </ol>
        </div>

        {/* Advanced Options */}
        <details className="space-y-2">
          <summary className="cursor-pointer font-semibold">Advanced Options</summary>
          <div className="space-y-2 pl-4">
            <Button size="sm" variant="outline" onClick={() => {
              localStorage.clear();
              window.location.reload();
            }}>
              Clear All Data & Refresh
            </Button>
            <Button size="sm" variant="outline" onClick={() => {
              // Clear only user data, keep mock games
              localStorage.removeItem('mafia_guest_user');
              window.location.reload();
            }}>
              Clear User Data Only
            </Button>
            <p className="text-xs text-muted-foreground">
              Use these options if you encounter authentication issues
            </p>
          </div>
        </details>
      </CardContent>
    </Card>
  );
};

export default UserManager;
