import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { ExternalLink, Users, Copy } from 'lucide-react';

const SimpleMultiTabTester: React.FC = () => {
  const { user, signOut, signInAnonymously } = useAuth();
  const { toast } = useToast();
  const [gameId, setGameId] = useState('');

  const handleQuickUserCreation = async (userName: string) => {
    try {
      await signOut();
      await new Promise(resolve => setTimeout(resolve, 500));
      await signInAnonymously(userName);
      
      toast({
        title: "User Created",
        description: `Now logged in as: ${userName}`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create user",
        variant: "destructive"
      });
    }
  };

  const copyGameId = () => {
    if (gameId) {
      navigator.clipboard.writeText(gameId);
      toast({
        title: "Copied!",
        description: `Game ID ${gameId} copied to clipboard`,
      });
    }
  };

  const copyCurrentUrl = () => {
    navigator.clipboard.writeText(window.location.href);
    toast({
      title: "URL Copied",
      description: "Current URL copied. Open in new tab and create different user.",
    });
  };

  const joinGame = () => {
    if (gameId.trim()) {
      window.location.href = `/waiting/${gameId}`;
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5" />
          Simple Multi-Tab Tester (Fallback)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current User Info */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold mb-2">Current User</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm">
                <strong>User:</strong> {user ? (user.user_metadata?.name || 'Guest') : 'Not logged in'}
              </p>
              <p className="text-sm text-muted-foreground">
                <strong>ID:</strong> {user ? user.id.slice(-8) : 'None'}
              </p>
            </div>
            <Badge variant={user ? 'default' : 'secondary'}>
              {user ? 'Logged In' : 'No User'}
            </Badge>
          </div>
        </div>

        {/* Quick User Creation */}
        <div className="space-y-3">
          <h3 className="font-semibold">Create User in Current Tab</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button size="sm" variant="outline" onClick={() => handleQuickUserCreation('Host Player')}>
              Host Player
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleQuickUserCreation('Player 2')}>
              Player 2
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleQuickUserCreation('Player 3')}>
              Player 3
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleQuickUserCreation('Player 4')}>
              Player 4
            </Button>
          </div>
        </div>

        {/* Manual Tab Creation */}
        <div className="space-y-3">
          <h3 className="font-semibold">Manual Multi-Tab Setup</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" onClick={copyCurrentUrl}>
              <Copy className="w-4 h-4 mr-2" />
              Copy URL for New Tab
            </Button>
            <Button variant="outline" onClick={() => window.open(window.location.href, '_blank')}>
              <ExternalLink className="w-4 h-4 mr-2" />
              Open New Tab
            </Button>
          </div>
        </div>

        {/* Game ID Management */}
        <div className="space-y-3">
          <h3 className="font-semibold">Game ID for Testing</h3>
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="simple-game-id">Game ID</Label>
              <Input
                id="simple-game-id"
                placeholder="Enter or paste game ID"
                value={gameId}
                onChange={(e) => setGameId(e.target.value)}
              />
            </div>
            <div className="flex items-end gap-2">
              <Button size="sm" variant="outline" onClick={copyGameId} disabled={!gameId}>
                <Copy className="w-4 h-4 mr-1" />
                Copy
              </Button>
              <Button size="sm" onClick={joinGame} disabled={!gameId || !user}>
                Join Game
              </Button>
            </div>
          </div>
        </div>

        {/* Simple Instructions */}
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-semibold mb-2">Simple Multi-Tab Testing Steps:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li><strong>Tab 1:</strong> Create "Host Player" → Create mock game → Copy game ID</li>
            <li><strong>Click "Copy URL for New Tab"</strong> → Open in new browser tab</li>
            <li><strong>Tab 2:</strong> Create "Player 2" → Paste game ID → Join game</li>
            <li><strong>Repeat for Tab 3:</strong> Create "Player 3" → Join same game</li>
            <li><strong>All tabs:</strong> Mark ready → Host starts game → Test flow</li>
          </ol>
        </div>

        {/* Session Storage Info */}
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="font-semibold mb-1">Session Storage Info:</h4>
          <p className="text-sm">
            Each browser tab now uses sessionStorage for user data, so each tab can have a different user.
            If you're still seeing shared users, try refreshing all tabs or using incognito/private browsing.
          </p>
        </div>

        {/* Debug Info */}
        <details className="space-y-2">
          <summary className="cursor-pointer font-semibold text-sm">Debug Information</summary>
          <div className="p-3 bg-muted rounded-lg text-xs">
            <p><strong>SessionStorage User:</strong> {sessionStorage.getItem('mafia_guest_user') ? 'Found' : 'None'}</p>
            <p><strong>LocalStorage User:</strong> {localStorage.getItem('mafia_guest_user') ? 'Found' : 'None'}</p>
            <p><strong>Current URL:</strong> {window.location.href}</p>
            <p><strong>User Agent:</strong> {navigator.userAgent.slice(0, 100)}...</p>
          </div>
        </details>
      </CardContent>
    </Card>
  );
};

export default SimpleMultiTabTester;
