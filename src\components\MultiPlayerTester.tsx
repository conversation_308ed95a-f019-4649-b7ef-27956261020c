import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { Co<PERSON>, Users, Crown, Zap } from 'lucide-react';

const MultiPlayerTester: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [mockGames, setMockGames] = useState<any[]>([]);
  const [selectedGameId, setSelectedGameId] = useState('');

  // Scan localStorage for mock games
  useEffect(() => {
    const games: any[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('mock_game_')) {
        try {
          const gameData = localStorage.getItem(key);
          if (gameData) {
            const parsed = JSON.parse(gameData);
            games.push(parsed);
          }
        } catch (error) {
          console.error('Error parsing mock game:', error);
        }
      }
    }
    setMockGames(games);
  }, []);

  const copyGameId = (gameId: string) => {
    navigator.clipboard.writeText(gameId);
    toast({
      title: "Copied!",
      description: `Game ID ${gameId} copied to clipboard`,
    });
  };

  const openNewTab = (gameId: string) => {
    const url = `${window.location.origin}/waiting/${gameId}`;
    window.open(url, '_blank');
    toast({
      title: "New Tab Opened",
      description: "Use this tab to join as a different player",
    });
  };

  const getPlayersInGame = (gameId: string) => {
    const players: any[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(`mock_player_${gameId}_`)) {
        try {
          const playerData = localStorage.getItem(key);
          if (playerData) {
            players.push(JSON.parse(playerData));
          }
        } catch (error) {
          console.error('Error parsing player:', error);
        }
      }
    }
    return players;
  };

  const clearMockGame = (gameId: string) => {
    // Remove game and all players
    localStorage.removeItem(`mock_game_${gameId}`);
    
    // Remove all players for this game
    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i);
      if (key && key.startsWith(`mock_player_${gameId}_`)) {
        localStorage.removeItem(key);
      }
    }
    
    // Refresh the list
    setMockGames(prev => prev.filter(g => g.id !== gameId));
    
    toast({
      title: "Mock Game Cleared",
      description: `Game ${gameId} and all players removed`,
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5" />
          Multi-Player Testing Helper
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {user && (
          <div className="p-3 bg-blue-50 rounded-lg text-sm">
            <p><strong>Current User:</strong> {user.user_metadata?.name || user.email || 'Guest'}</p>
            <p><strong>User ID:</strong> {user.id}</p>
          </div>
        )}

        <div className="space-y-3">
          <h3 className="font-semibold">Available Mock Games:</h3>
          
          {mockGames.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              No mock games found. Create one using the Mock Game Creator above.
            </div>
          ) : (
            mockGames.map((game) => {
              const players = getPlayersInGame(game.id);
              const isHost = user && game.host_id === user.id;
              
              return (
                <Card key={game.id} className="border-2">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">MOCK</Badge>
                        {isHost && <Crown className="w-4 h-4 text-amber-500" />}
                        <span className="font-medium">{game.game_name}</span>
                      </div>
                      <Badge variant="outline">{game.id}</Badge>
                    </div>
                    
                    <div className="space-y-2 mb-3">
                      <div className="text-sm text-muted-foreground">
                        Players: {players.length}/{game.max_players} | 
                        Status: {game.status} | 
                        Mafia: {game.mafia_count}
                      </div>
                      
                      {players.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {players.map((player) => (
                            <Badge 
                              key={player.id} 
                              variant={player.is_ready ? "default" : "secondary"}
                              className="text-xs"
                            >
                              {player.name} {player.is_ready ? "✓" : "○"}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex gap-2 flex-wrap">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyGameId(game.id)}
                      >
                        <Copy className="w-3 h-3 mr-1" />
                        Copy ID
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openNewTab(game.id)}
                      >
                        <Zap className="w-3 h-3 mr-1" />
                        Open in New Tab
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.location.href = `/waiting/${game.id}`}
                      >
                        Join Game
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => clearMockGame(game.id)}
                      >
                        Clear
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>

        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
          <h4 className="font-semibold mb-2">Multi-Player Testing Instructions:</h4>
          <ol className="list-decimal list-inside space-y-1">
            <li>Create a mock game using the Mock Game Creator</li>
            <li>Click "Copy ID" to copy the game ID</li>
            <li>Click "Open in New Tab" to open additional browser tabs</li>
            <li>In each new tab, authenticate as a different guest user</li>
            <li>Use "Join Game" with the copied game ID</li>
            <li>Test ready status, game start, and role assignment</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default MultiPlayerTester;
