import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';

const DatabaseFixer: React.FC = () => {
  const { user } = useAuth();
  const [fixResults, setFixResults] = useState<string[]>([]);
  const [isFixing, setIsFixing] = useState(false);

  const addResult = (message: string, isError = false) => {
    const timestamp = new Date().toLocaleTimeString();
    const result = `[${timestamp}] ${isError ? '❌' : '✅'} ${message}`;
    setFixResults(prev => [...prev, result]);
    console.log(result);
  };

  const fixDatabaseSchema = async () => {
    setIsFixing(true);
    setFixResults([]);

    try {
      addResult('Starting database schema fix...');

      // Test current schema
      addResult('Testing current schema...');
      
      // Try to create a test game to see what error we get
      const testGameId = `SCHEMA_TEST_${Date.now()}`;
      const { data: testData, error: testError } = await supabase
        .from('games')
        .insert({
          id: testGameId,
          host_id: user?.id || 'test_host',
          game_name: 'Schema Test',
          max_players: 3,
          mafia_count: 1,
          discussion_time: 60,
          is_public: false,
          status: 'waiting'
        })
        .select();

      if (testError) {
        addResult(`Current schema error: ${testError.message}`, true);
        addResult(`Error code: ${testError.code}`, true);
        addResult(`Error details: ${testError.details}`, true);
        addResult(`Error hint: ${testError.hint}`, true);
        
        if (testError.message.includes('foreign key') || testError.code === '23503') {
          addResult('Foreign key constraint detected - this is the issue!');
          addResult('The host_id field has a foreign key to auth.users but guest users are not in that table');
          addResult('Solution: Remove foreign key constraints or use different approach');
        }
      } else {
        addResult('Schema test passed - no foreign key issues detected');
        // Clean up test data
        await supabase.from('games').delete().eq('id', testGameId);
        addResult('Test data cleaned up');
      }

      // Test with a real authenticated user ID if available
      if (user && !user.id.startsWith('guest_')) {
        addResult('Testing with authenticated user...');
        const authTestId = `AUTH_TEST_${Date.now()}`;
        const { data: authData, error: authError } = await supabase
          .from('games')
          .insert({
            id: authTestId,
            host_id: user.id,
            game_name: 'Auth Test',
            max_players: 3,
            mafia_count: 1,
            discussion_time: 60,
            is_public: false,
            status: 'waiting'
          })
          .select();

        if (authError) {
          addResult(`Auth user test failed: ${authError.message}`, true);
        } else {
          addResult('Auth user test passed');
          await supabase.from('games').delete().eq('id', authTestId);
        }
      }

      // Provide recommendations
      addResult('=== RECOMMENDATIONS ===');
      if (testError && (testError.message.includes('foreign key') || testError.code === '23503')) {
        addResult('1. Database schema needs to be updated to support guest users');
        addResult('2. Remove foreign key constraints on host_id and user_id fields');
        addResult('3. Change host_id and user_id from UUID to TEXT');
        addResult('4. Update RLS policies to work with guest user IDs');
        addResult('5. Use the Mock Game Creator for testing until schema is fixed');
      } else {
        addResult('Schema appears to be working correctly');
        addResult('Try using the Simple Game Creator');
      }

    } catch (error: any) {
      addResult(`Unexpected error: ${error.message}`, true);
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Database Schema Fixer</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <Button 
            onClick={fixDatabaseSchema} 
            disabled={isFixing}
            className="w-full"
            variant="destructive"
          >
            {isFixing ? 'Analyzing Schema...' : 'Diagnose Database Issues'}
          </Button>
        </div>

        {user && (
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm">
              <strong>Current User:</strong> {user.user_metadata?.name || user.email || user.id}
            </p>
            <p className="text-sm">
              <strong>User ID:</strong> {user.id}
            </p>
            <Badge variant={user.id.startsWith('guest_') ? 'secondary' : 'default'}>
              {user.id.startsWith('guest_') ? 'Guest User' : 'Authenticated User'}
            </Badge>
          </div>
        )}

        {fixResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Diagnosis Results:</h3>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
              {fixResults.map((result, index) => (
                <div key={index} className={result.includes('❌') ? 'text-red-400' : result.includes('===') ? 'text-yellow-400 font-bold' : 'text-green-400'}>
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DatabaseFixer;
