import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { Copy, RefreshCw, Search, Trash2 } from 'lucide-react';

const MockGameDebugger: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testGameId, setTestGameId] = useState('');
  const [refreshKey, setRefreshKey] = useState(0);

  const scanLocalStorage = () => {
    const info: any = {
      mockGames: [],
      mockPlayers: [],
      mockVotes: [],
      mockMessages: [],
      totalItems: localStorage.length,
      allKeys: []
    };

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        info.allKeys.push(key);
        
        try {
          const value = localStorage.getItem(key);
          if (value) {
            const parsed = JSON.parse(value);
            
            if (key.startsWith('mock_game_')) {
              info.mockGames.push({ key, data: parsed });
            } else if (key.startsWith('mock_player_')) {
              info.mockPlayers.push({ key, data: parsed });
            } else if (key.startsWith('mock_vote_')) {
              info.mockVotes.push({ key, data: parsed });
            } else if (key.startsWith('mock_message_')) {
              info.mockMessages.push({ key, data: parsed });
            }
          }
        } catch (error) {
          console.error('Error parsing localStorage item:', key, error);
        }
      }
    }

    setDebugInfo(info);
  };

  useEffect(() => {
    scanLocalStorage();
  }, [refreshKey]);

  const refresh = () => {
    setRefreshKey(prev => prev + 1);
    toast({
      title: "Refreshed",
      description: "LocalStorage data refreshed",
    });
  };

  const clearAllMockData = () => {
    const keysToRemove: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('mock_game_') || key.startsWith('mock_player_') || 
                  key.startsWith('mock_vote_') || key.startsWith('mock_message_'))) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    toast({
      title: "Cleared",
      description: `Removed ${keysToRemove.length} mock data items`,
    });
    
    refresh();
  };

  const testGameLoad = () => {
    if (!testGameId.trim()) return;
    
    console.log('=== TESTING GAME LOAD ===');
    console.log('Game ID:', testGameId);
    
    const gameKey = `mock_game_${testGameId}`;
    const gameData = localStorage.getItem(gameKey);
    
    console.log('Game key:', gameKey);
    console.log('Game data exists:', gameData !== null);
    console.log('Game data:', gameData);
    
    if (gameData) {
      try {
        const parsed = JSON.parse(gameData);
        console.log('Parsed game data:', parsed);
        
        // Check for players
        const playerKeys: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith(`mock_player_${testGameId}_`)) {
            playerKeys.push(key);
          }
        }
        
        console.log('Player keys found:', playerKeys);
        
        toast({
          title: "Game Found",
          description: `Game exists with ${playerKeys.length} players`,
        });
      } catch (error) {
        console.error('Error parsing game data:', error);
        toast({
          title: "Error",
          description: "Game data is corrupted",
          variant: "destructive"
        });
      }
    } else {
      toast({
        title: "Game Not Found",
        description: `No mock game found with ID: ${testGameId}`,
        variant: "destructive"
      });
    }
  };

  const copyGameId = (gameId: string) => {
    navigator.clipboard.writeText(gameId);
    toast({
      title: "Copied",
      description: `Game ID ${gameId} copied to clipboard`,
    });
  };

  const navigateToGame = (gameId: string) => {
    window.location.href = `/waiting/${gameId}`;
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Mock Game Debugger</span>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={refresh}>
              <RefreshCw className="w-4 h-4 mr-1" />
              Refresh
            </Button>
            <Button size="sm" variant="destructive" onClick={clearAllMockData}>
              <Trash2 className="w-4 h-4 mr-1" />
              Clear All
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current User Info */}
        {user && (
          <div className="p-3 bg-blue-50 rounded-lg text-sm">
            <p><strong>Current User:</strong> {user.user_metadata?.name || user.email || 'Guest'}</p>
            <p><strong>User ID:</strong> {user.id}</p>
          </div>
        )}

        {/* Test Game Load */}
        <div className="space-y-2">
          <Label>Test Game Load</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Enter Game ID to test"
              value={testGameId}
              onChange={(e) => setTestGameId(e.target.value)}
            />
            <Button onClick={testGameLoad}>
              <Search className="w-4 h-4 mr-1" />
              Test Load
            </Button>
          </div>
        </div>

        {/* Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="text-2xl font-bold">{debugInfo.mockGames?.length || 0}</div>
            <div className="text-sm text-muted-foreground">Mock Games</div>
          </div>
          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="text-2xl font-bold">{debugInfo.mockPlayers?.length || 0}</div>
            <div className="text-sm text-muted-foreground">Mock Players</div>
          </div>
          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="text-2xl font-bold">{debugInfo.mockVotes?.length || 0}</div>
            <div className="text-sm text-muted-foreground">Mock Votes</div>
          </div>
          <div className="text-center p-3 bg-muted rounded-lg">
            <div className="text-2xl font-bold">{debugInfo.mockMessages?.length || 0}</div>
            <div className="text-sm text-muted-foreground">Mock Messages</div>
          </div>
        </div>

        {/* Mock Games */}
        {debugInfo.mockGames?.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Mock Games:</h3>
            {debugInfo.mockGames.map((item: any, index: number) => (
              <Card key={index} className="border-2">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">MOCK</Badge>
                      <span className="font-medium">{item.data.game_name}</span>
                    </div>
                    <Badge variant="outline">{item.data.id}</Badge>
                  </div>
                  
                  <div className="text-sm text-muted-foreground mb-3">
                    Status: {item.data.status} | Host: {item.data.host_id} | 
                    Max Players: {item.data.max_players} | Mafia: {item.data.mafia_count}
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Button size="sm" variant="outline" onClick={() => copyGameId(item.data.id)}>
                      <Copy className="w-3 h-3 mr-1" />
                      Copy ID
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => navigateToGame(item.data.id)}>
                      Join Game
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => setTestGameId(item.data.id)}>
                      Test Load
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Debug Info */}
        <details className="space-y-2">
          <summary className="cursor-pointer font-semibold">Raw Debug Data</summary>
          <pre className="bg-black text-green-400 p-4 rounded-lg text-xs overflow-auto max-h-64">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  );
};

export default MockGameDebugger;
