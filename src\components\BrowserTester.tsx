import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Monitor, Smartphone, ExternalLink, Copy } from 'lucide-react';

const BrowserTester: React.FC = () => {
  const { toast } = useToast();

  const getBrowserInfo = () => {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';

    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      browserName = 'Chrome';
      const match = userAgent.match(/Chrome\/(\d+)/);
      browserVersion = match ? match[1] : 'Unknown';
    } else if (userAgent.includes('Firefox')) {
      browserName = 'Firefox';
      const match = userAgent.match(/Firefox\/(\d+)/);
      browserVersion = match ? match[1] : 'Unknown';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      browserName = 'Safari';
      const match = userAgent.match(/Version\/(\d+)/);
      browserVersion = match ? match[1] : 'Unknown';
    } else if (userAgent.includes('Edg')) {
      browserName = 'Edge';
      const match = userAgent.match(/Edg\/(\d+)/);
      browserVersion = match ? match[1] : 'Unknown';
    }

    return { browserName, browserVersion };
  };

  const getDeviceInfo = () => {
    const userAgent = navigator.userAgent;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isTablet = /iPad|Android(?=.*\bMobile\b)/i.test(userAgent);
    
    return {
      isMobile,
      isTablet,
      isDesktop: !isMobile && !isTablet,
      platform: navigator.platform,
      language: navigator.language
    };
  };

  const { browserName, browserVersion } = getBrowserInfo();
  const deviceInfo = getDeviceInfo();

  const copyCurrentUrl = () => {
    navigator.clipboard.writeText(window.location.href);
    toast({
      title: "URL Copied",
      description: "Current URL copied to clipboard",
    });
  };

  const openInNewBrowser = () => {
    const url = window.location.href;
    toast({
      title: "Cross-Browser Testing",
      description: "Copy this URL and open it in a different browser for testing",
    });
    navigator.clipboard.writeText(url);
  };

  const testUrls = [
    { name: 'Chrome', url: 'googlechrome://' + window.location.href },
    { name: 'Firefox', url: 'firefox://' + window.location.href },
    { name: 'Safari', url: 'safari://' + window.location.href },
    { name: 'Edge', url: 'microsoft-edge://' + window.location.href }
  ];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Monitor className="w-5 h-5" />
          Browser & Cross-Platform Testing
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Browser Info */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold mb-3">Current Environment</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm">
                <strong>Browser:</strong> {browserName} {browserVersion}
              </p>
              <p className="text-sm">
                <strong>Platform:</strong> {deviceInfo.platform}
              </p>
              <p className="text-sm">
                <strong>Language:</strong> {deviceInfo.language}
              </p>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge variant={deviceInfo.isDesktop ? 'default' : 'secondary'}>
                {deviceInfo.isDesktop && <Monitor className="w-3 h-3 mr-1" />}
                Desktop
              </Badge>
              <Badge variant={deviceInfo.isMobile ? 'default' : 'secondary'}>
                {deviceInfo.isMobile && <Smartphone className="w-3 h-3 mr-1" />}
                Mobile
              </Badge>
              <Badge variant={deviceInfo.isTablet ? 'default' : 'secondary'}>
                Tablet
              </Badge>
            </div>
          </div>
        </div>

        {/* Cross-Browser Testing */}
        <div className="space-y-3">
          <h3 className="font-semibold">Cross-Browser Testing</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Button variant="outline" onClick={copyCurrentUrl}>
              <Copy className="w-4 h-4 mr-2" />
              Copy Current URL
            </Button>
            <Button variant="outline" onClick={openInNewBrowser}>
              <ExternalLink className="w-4 h-4 mr-2" />
              Copy for Other Browser
            </Button>
          </div>
        </div>

        {/* Browser Compatibility */}
        <div className="space-y-3">
          <h3 className="font-semibold">Browser Compatibility</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="font-medium">Chrome</div>
              <Badge variant="default" className="mt-1">Recommended</Badge>
            </div>
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="font-medium">Firefox</div>
              <Badge variant="default" className="mt-1">Supported</Badge>
            </div>
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="font-medium">Safari</div>
              <Badge variant="secondary" className="mt-1">Limited</Badge>
            </div>
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="font-medium">Edge</div>
              <Badge variant="default" className="mt-1">Supported</Badge>
            </div>
          </div>
        </div>

        {/* Testing Features */}
        <div className="space-y-3">
          <h3 className="font-semibold">Feature Support</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">WebRTC (Video Chat)</span>
                <Badge variant={navigator.mediaDevices ? 'default' : 'destructive'}>
                  {navigator.mediaDevices ? 'Supported' : 'Not Supported'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">LocalStorage</span>
                <Badge variant={typeof Storage !== 'undefined' ? 'default' : 'destructive'}>
                  {typeof Storage !== 'undefined' ? 'Supported' : 'Not Supported'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">SessionStorage</span>
                <Badge variant={typeof sessionStorage !== 'undefined' ? 'default' : 'destructive'}>
                  {typeof sessionStorage !== 'undefined' ? 'Supported' : 'Not Supported'}
                </Badge>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Clipboard API</span>
                <Badge variant={navigator.clipboard ? 'default' : 'secondary'}>
                  {navigator.clipboard ? 'Supported' : 'Limited'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Notifications</span>
                <Badge variant={typeof Notification !== 'undefined' ? 'default' : 'destructive'}>
                  {typeof Notification !== 'undefined' ? 'Supported' : 'Not Supported'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Service Workers</span>
                <Badge variant={'serviceWorker' in navigator ? 'default' : 'destructive'}>
                  {'serviceWorker' in navigator ? 'Supported' : 'Not Supported'}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Testing Instructions */}
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="font-semibold mb-2">Cross-Browser Testing Instructions:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li><strong>Same Browser, Multiple Tabs:</strong> Use Multi-Tab Tester above</li>
            <li><strong>Different Browsers:</strong> Copy URL and open in Chrome, Firefox, Safari, Edge</li>
            <li><strong>Mobile Testing:</strong> Copy URL and open on mobile devices</li>
            <li><strong>Incognito/Private:</strong> Test in private browsing modes</li>
            <li><strong>Network Testing:</strong> Test on different networks (WiFi, mobile data)</li>
          </ol>
        </div>

        {/* Current URL Display */}
        <div className="p-3 bg-muted rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex-1 mr-2">
              <p className="text-xs text-muted-foreground">Current URL:</p>
              <p className="text-sm font-mono break-all">{window.location.href}</p>
            </div>
            <Button size="sm" variant="outline" onClick={copyCurrentUrl}>
              <Copy className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BrowserTester;
