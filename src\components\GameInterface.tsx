import { useState, useEffect } from 'react';
import { useGame } from '@/hooks/useGame';
import { useAuth } from '@/hooks/useAuth';
import { useGameTimer } from '@/hooks/useGameTimer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Crown, Skull, Eye, Heart, MessageCircle, Vote, Clock, EyeOff, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import VideoChat from './VideoChat';
import GameEndScreen from './GameEndScreen';
import EliminationResult from './EliminationResult';

interface GameInterfaceProps {
  gameId: string;
}

const GameInterface = ({ gameId }: GameInterfaceProps) => {
  const { user } = useAuth();
  const { game, players, votes, messages, currentPlayer, vote, sendMessage, tallyVotesAndEliminate } = useGame(gameId);
  const { timeLeft, formatTime } = useGameTimer(game);
  const [selectedPlayer, setSelectedPlayer] = useState<string>('');
  const [message, setMessage] = useState('');

  // Debug logging
  useEffect(() => {
    console.log('GameInterface - Game state:', {
      gameId,
      game: game ? {
        id: game.id,
        status: game.status,
        current_phase_end: game.current_phase_end,
        winner: game.winner
      } : null,
      playersCount: players.length,
      currentPlayer: currentPlayer ? {
        name: currentPlayer.name,
        role: currentPlayer.role,
        is_alive: currentPlayer.is_alive
      } : null,
      timeLeft
    });
  }, [game, players, currentPlayer, timeLeft]);

  const alivePlayers = players.filter(p => p.is_alive);
  const deadPlayers = players.filter(p => !p.is_alive);
  const myVotes = votes.filter(v => v.voter_id === currentPlayer?.id);

  // Timer is now handled by useGameTimer hook

  const handleVote = (targetId: string) => {
    const voteType = game?.status === 'voting' ? 'eliminate' : 'mafia_kill';
    vote(targetId, voteType);
    setSelectedPlayer('');
  };

  const skipToPhase = (newPhase: string) => {
    if (!gameId || !game) return;

    // If skipping from a voting phase, tally votes first
    if (game.status === 'voting') {
      console.log('🚀 Skipping voting phase - tallying votes first');
      tallyVotesAndEliminate('eliminate');
    }

    const now = new Date();
    let phaseDuration = 0;

    switch (newPhase) {
      case 'mafia_reveal':
        phaseDuration = 5 * 1000;
        break;
      case 'discussion':
        phaseDuration = 120 * 1000;
        break;
      case 'voting':
        phaseDuration = 60 * 1000;
        break;
      case 'final_discussion':
        phaseDuration = 60 * 1000;
        break;
      case 'ended':
        phaseDuration = 0;
        break;
    }

    const phaseEndTime = phaseDuration > 0 ? new Date(now.getTime() + phaseDuration) : null;

    const updatedGame = {
      ...game,
      status: newPhase,
      current_phase_end: phaseEndTime?.toISOString() || null
    };

    localStorage.setItem(`mock_game_${gameId}`, JSON.stringify(updatedGame));

    // Trigger storage event for real-time updates
    window.dispatchEvent(new StorageEvent('storage', {
      key: `mock_game_${gameId}`,
      newValue: JSON.stringify(updatedGame),
      storageArea: localStorage
    }));
  };

  const handleSendMessage = () => {
    if (!message.trim()) return;
    const isMafiaChat = false; // No mafia chat in simplified version
    sendMessage(message, isMafiaChat);
    setMessage('');
  };

  // formatTime is now provided by useGameTimer hook

  const getPhaseTitle = () => {
    switch (game?.status) {
      case 'mafia_reveal':
        return 'Mafia Reveal Phase';
      case 'discussion':
        return 'Discussion Phase';
      case 'voting':
        return 'Voting Phase';
      case 'final_discussion':
        return 'Final Discussion';
      case 'ended':
        return `Game Over - ${game.winner === 'civilian' ? 'Civilians' : 'Mafia'} Win!`;
      default:
        return 'Unknown Phase';
    }
  };

  const getPhaseDescription = () => {
    if (!currentPlayer) return '';

    switch (game?.status) {
      case 'mafia_reveal':
        if (currentPlayer.role === 'mafia') {
          const otherMafia = players.filter(p => p.role === 'mafia' && p.id !== currentPlayer.id);
          return `You are MAFIA! ${otherMafia.length > 0 ? `Your partner(s): ${otherMafia.map(p => p.name).join(', ')}` : 'You are the only mafia.'}`;
        }
        return 'You are a CIVILIAN. The mafia are revealing themselves to each other.';

      case 'discussion':
        return 'Discuss who you think might be mafia. Use this time to share information and suspicions.';

      case 'voting':
        return 'Vote to eliminate someone you suspect is mafia. The player with the most votes will be eliminated.';

      case 'final_discussion':
        return 'Game over! Discuss the game and reveal strategies.';

      default:
        return '';
    }
  };

  const getRoleIcon = (role?: string) => {
    switch (role) {
      case 'mafia':
        return <Skull className="w-4 h-4 text-destructive" />;
      case 'detective':
        return <Eye className="w-4 h-4 text-blue-500" />;
      case 'doctor':
        return <Heart className="w-4 h-4 text-green-500" />;
      default:
        return <Crown className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const canVote = () => {
    if (!currentPlayer?.is_alive) return false;
    if (game?.status === 'voting') return true;
    return false;
  };

  const getVotableTargets = () => {
    if (game?.status === 'voting') {
      // During voting, can vote for any alive player except self
      return players.filter(p => p.is_alive && p.id !== currentPlayer?.id);
    }
    return [];
  };

  const shouldShowPlayer = (player: any) => {
    // Always show yourself
    if (player.id === currentPlayer?.id) return true;

    // During mafia reveal, only show other mafia to mafia players
    if (game?.status === 'mafia_reveal') {
      if (currentPlayer?.role === 'mafia' && player.role === 'mafia') return true;
      if (currentPlayer?.role !== 'mafia' && player.role !== 'mafia') return true;
      return false;
    }

    // During other phases, show all players but hide roles (except for mafia seeing other mafia)
    return true;
  };

  const shouldShowRole = (player: any) => {
    // Always show your own role
    if (player.id === currentPlayer?.id) return true;

    // During mafia reveal, mafia can see other mafia
    if (game?.status === 'mafia_reveal' && currentPlayer?.role === 'mafia' && player.role === 'mafia') {
      return true;
    }

    // During final discussion or game end, show all roles
    if (game?.status === 'final_discussion' || game?.status === 'ended') {
      return true;
    }

    // No special night phase visibility in simplified version

    return false;
  };



  if (!game || !currentPlayer) {
    return <div>Loading...</div>;
  }

  // Show end screen if game is over
  if (game.status === 'ended') {
    return <GameEndScreen game={game} players={players} currentPlayer={currentPlayer} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted to-background p-4">
      <div className="max-w-7xl mx-auto space-y-6">

        {/* Video Chat Section */}
        <VideoChat
          gameId={gameId}
          players={players}
          currentPlayer={currentPlayer}
          gameStatus={game.status}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Game Status & Players */}
          <div className="lg:col-span-2 space-y-6">
          
          {/* Phase Header */}
          <Card className={cn(
            "fade-in phase-indicator border-2",
            game?.status === 'mafia_reveal' && "border-red-500 bg-red-50",
            game?.status === 'voting' && "border-orange-500 bg-orange-50",
            game?.status === 'discussion' && "border-green-500 bg-green-50"
          )}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  {getPhaseTitle()}
                  <Badge
                    variant={game?.status === 'mafia_reveal' ? 'destructive' : 'default'}
                    className="text-sm"
                  >
                    {game?.status?.replace('_', ' ').toUpperCase()}
                  </Badge>
                </span>
                <Badge
                  variant="outline"
                  className={cn(
                    "text-lg px-3 py-1 transition-all",
                    timeLeft <= 10 && timeLeft > 0 && "bg-destructive/20 border-destructive text-destructive animate-pulse"
                  )}
                >
                  {formatTime()}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Phase Description */}
                <div className="p-3 bg-muted rounded-lg">
                  <p className="text-sm">{getPhaseDescription()}</p>
                </div>

                {/* Role Information */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {getRoleIcon(currentPlayer.role)}
                    <span className="font-semibold">
                      You are: {currentPlayer.role?.toUpperCase()}
                    </span>
                    <Badge variant={currentPlayer.is_alive ? "default" : "destructive"}>
                      {currentPlayer.is_alive ? "Alive" : "Dead"}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Special message for mafia reveal phase */}
              {game.status === 'mafia_reveal' && (
                <div className={cn(
                  "mt-4 p-4 rounded-lg border fade-in",
                  currentPlayer.role === 'mafia'
                    ? "bg-destructive/10 border-destructive/20 mafia-glow"
                    : "bg-muted/50 border-muted"
                )}>
                  {currentPlayer.role === 'mafia' ? (
                    <div className="flex items-center gap-2 text-destructive">
                      <Eye className="w-5 h-5" />
                      <span className="font-medium">
                        You can see your fellow mafia members. Civilians cannot see or hear anything during this phase.
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <EyeOff className="w-5 h-5" />
                      <span className="font-medium">
                        The mafia are identifying each other. You cannot see or hear anything during this phase.
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Phase description */}
              <div className="mt-4 p-3 rounded-lg bg-muted/50">
                <p className="text-sm text-muted-foreground text-center">
                  {getPhaseDescription()}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Players Display */}
          <Card>
            <CardHeader>
              <CardTitle>
                {game.status === 'voting' ?
                  `Vote to Eliminate (${alivePlayers.length} alive)` :
                  `Players (${alivePlayers.length} alive)`
                }
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {alivePlayers.map((player) => {
                  const hasVoted = myVotes.some(v => v.target_id === player.id);
                  const isVotable = getVotableTargets().some(p => p.id === player.id);
                  const showRole = shouldShowRole(player);

                  return (
                    <div
                      key={player.id}
                      className={cn(
                        "p-4 rounded-lg border bg-card transition-colors",
                        selectedPlayer === player.id && "ring-2 ring-primary",
                        isVotable && "cursor-pointer hover:bg-muted",
                        hasVoted && "bg-primary/10 border-primary",
                        !isVotable && canVote() && "opacity-50"
                      )}
                      onClick={() => isVotable ? setSelectedPlayer(player.id) : null}
                    >
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarFallback>
                            {player.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="font-medium">{player.name}</div>

                          {/* Role Display - Only when appropriate */}
                          {showRole && (
                            <Badge
                              variant={player.role === 'mafia' ? 'destructive' : 'default'}
                              className="text-xs mt-1"
                            >
                              {player.role?.toUpperCase()}
                            </Badge>
                          )}

                          {/* Voting Status */}
                          {hasVoted && canVote() && (
                            <Badge variant="outline" className="text-xs mt-1">
                              ✓ Voted
                            </Badge>
                          )}

                          {/* Voting Indicator */}
                          {isVotable && (
                            <Badge variant="secondary" className="text-xs mt-1">
                              Click to Vote
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Voting Section - Only During Voting Phases */}
          {canVote() && (
            <Card className="border-2 border-primary">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Vote className="w-5 h-5" />
                  Elimination Vote
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Select a player to vote for elimination. The player with the most votes will be eliminated.
                  </p>

                  {selectedPlayer ? (
                    <div className="space-y-3">
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="text-sm">
                          <strong>Selected:</strong> {players.find(p => p.id === selectedPlayer)?.name}
                        </p>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          onClick={() => handleVote(selectedPlayer)}
                          className="flex-1"
                          variant="destructive"
                        >
                          <Vote className="w-4 h-4 mr-2" />
                          Confirm Vote
                        </Button>
                        <Button
                          onClick={() => setSelectedPlayer('')}
                          variant="outline"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-center text-muted-foreground py-4">
                      Click on a player above to select your vote
                    </p>
                  )}

                  {/* Current Votes Display */}
                  {myVotes.length > 0 && (
                    <div className="pt-3 border-t">
                      <p className="text-sm font-medium mb-2">Your Current Vote:</p>
                      {myVotes.map(vote => (
                        <div key={vote.id} className="flex items-center gap-2 text-sm">
                          <Badge variant="outline">
                            {players.find(p => p.id === vote.target_id)?.name}
                          </Badge>
                          <span className="text-muted-foreground">
                            ({vote.vote_type === 'eliminate' ? 'Elimination' : 'Night Kill'})
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Eliminated Players */}
          {deadPlayers.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Skull className="w-5 h-5 text-destructive" />
                  Eliminated Players ({deadPlayers.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {deadPlayers.map((player) => (
                    <div
                      key={player.id}
                      className="p-4 rounded-lg border bg-destructive/5 border-destructive/20"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar className="opacity-60">
                          <AvatarFallback>
                            {player.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="font-medium text-destructive line-through">
                            {player.name}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="destructive" className="text-xs">
                              Eliminated
                            </Badge>
                            {player.role && (
                              <Badge variant="outline" className="text-xs">
                                Was: {player.role}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <Eye className="w-4 h-4" />
                          <span className="text-xs">Observer</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-3 rounded-lg bg-muted/50">
                  <p className="text-xs text-muted-foreground text-center">
                    Eliminated players can observe the game but cannot participate or be seen by living players
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Current Player Status */}
          {!currentPlayer.is_alive && (
            <Card className="border-destructive/50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3 text-destructive">
                  <Skull className="w-5 h-5" />
                  <div>
                    <div className="font-medium">You have been eliminated</div>
                    <p className="text-sm text-muted-foreground">
                      You can observe the game but cannot participate or be seen by other players
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Chat */}
        <div className="space-y-6">
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5" />
                Town Chat
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <ScrollArea className="flex-1 h-0">
                <div className="space-y-3">
                  {messages
                    .filter(msg => !msg.is_mafia_chat) // Only show town chat in simplified version
                    .map((msg) => (
                      <div key={msg.id} className="flex gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarFallback className="text-xs">
                            {msg.player?.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">
                              {msg.player?.name}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(msg.created_at).toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="text-sm">{msg.message}</p>
                        </div>
                      </div>
                    ))}
                </div>
              </ScrollArea>
              
              <Separator className="my-4" />
              
              <div className="flex gap-2">
                <Input
                  placeholder="Send message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' ? handleSendMessage() : null}
                  disabled={!currentPlayer.is_alive}
                />
                <Button 
                  onClick={handleSendMessage}
                  disabled={!message.trim() || !currentPlayer.is_alive}
                >
                  Send
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Skip Phase Controls - Testing Only */}
          {gameId && localStorage.getItem(`mock_game_${gameId}`) && (
            <Card className="border-dashed border-2 border-muted-foreground/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Settings className="w-4 h-4" />
                  Skip Phase Controls (Testing)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-xs text-muted-foreground">
                    Skip to any phase for testing. Voting phases will tally votes before skipping.
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    <Button size="sm" variant="outline" onClick={() => skipToPhase('mafia_reveal')}>
                      Skip to Mafia Reveal
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => skipToPhase('discussion')}>
                      Skip to Discussion
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => skipToPhase('voting')}>
                      Skip to Voting
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => skipToPhase('final_discussion')}>
                      Skip to Final Discussion
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => skipToPhase('ended')}>
                      Skip to End Game
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          </div>
        </div>
      </div>

      {/* Elimination Result Overlay */}
      <EliminationResult gameId={gameId} />
    </div>
  );
};

export default GameInterface;