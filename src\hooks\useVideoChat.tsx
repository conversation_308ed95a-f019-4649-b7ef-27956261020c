import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from './useAuth';
import { Player } from './useGame';

export interface VideoStream {
  playerId: string;
  stream: MediaStream;
  isLocal: boolean;
}

export interface VideoControls {
  isCameraOn: boolean;
  isMicOn: boolean;
  toggleCamera: () => void;
  toggleMic: () => void;
}

export const useVideoChat = (gameId: string, players: Player[], currentPlayer: Player | null) => {
  const { user } = useAuth();
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStreams, setRemoteStreams] = useState<Map<string, MediaStream>>(new Map());
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [isMicOn, setIsMicOn] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const peersRef = useRef<Map<string, any>>(new Map());

  // Initialize local media stream
  const initializeMedia = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      setLocalStream(stream);
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }
      setIsInitialized(true);
    } catch (error) {
      console.error('Error accessing media devices:', error);
    }
  }, []);

  // Toggle camera
  const toggleCamera = useCallback(() => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsCameraOn(videoTrack.enabled);
      }
    }
  }, [localStream]);

  // Toggle microphone
  const toggleMic = useCallback(() => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsMicOn(audioTrack.enabled);
      }
    }
  }, [localStream]);

  // Initialize media on mount
  useEffect(() => {
    if (gameId && currentPlayer && !isInitialized) {
      initializeMedia();
    }
  }, [gameId, currentPlayer, isInitialized, initializeMedia]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
      peersRef.current.forEach(peer => {
        peer.destroy();
      });
    };
  }, [localStream]);

  const videoControls: VideoControls = {
    isCameraOn,
    isMicOn,
    toggleCamera,
    toggleMic
  };

  const videoStreams: VideoStream[] = [
    ...(localStream ? [{
      playerId: currentPlayer?.id || '',
      stream: localStream,
      isLocal: true
    }] : []),
    ...Array.from(remoteStreams.entries()).map(([playerId, stream]) => ({
      playerId,
      stream,
      isLocal: false
    }))
  ];

  return {
    localStream,
    remoteStreams,
    videoStreams,
    videoControls,
    localVideoRef,
    isInitialized
  };
};
