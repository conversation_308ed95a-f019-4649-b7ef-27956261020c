import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';

interface RealTimeStatusProps {
  gameId?: string;
  className?: string;
}

const RealTimeStatus: React.FC<RealTimeStatusProps> = ({ gameId, className = '' }) => {
  const [lastUpdate, setLastUpdate] = useState(Date.now());
  const [updateCount, setUpdateCount] = useState(0);
  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    if (!gameId) return;

    const isMockGame = localStorage.getItem(`mock_game_${gameId}`) !== null;
    if (!isMockGame) return;

    console.log('Setting up real-time status monitoring for:', gameId);

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key && (
        e.key.startsWith(`mock_game_${gameId}`) ||
        e.key.startsWith(`mock_player_${gameId}_`) ||
        e.key.startsWith(`mock_vote_${gameId}_`) ||
        e.key.startsWith(`mock_message_${gameId}_`)
      )) {
        console.log('Real-time update detected:', e.key);
        setLastUpdate(Date.now());
        setUpdateCount(prev => prev + 1);
        setIsConnected(true);
      }
    };

    // Check connection status
    const checkConnection = () => {
      const now = Date.now();
      const timeSinceLastUpdate = now - lastUpdate;
      
      // Consider disconnected if no updates for more than 10 seconds
      setIsConnected(timeSinceLastUpdate < 10000);
    };

    // Set up listeners and intervals
    window.addEventListener('storage', handleStorageChange);
    const connectionInterval = setInterval(checkConnection, 1000);
    const heartbeatInterval = setInterval(() => {
      setLastUpdate(Date.now());
    }, 5000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(connectionInterval);
      clearInterval(heartbeatInterval);
    };
  }, [gameId, lastUpdate]);

  const formatTimeAgo = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  if (!gameId || !localStorage.getItem(`mock_game_${gameId}`)) {
    return null;
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="p-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className="w-4 h-4 text-green-500" />
            ) : (
              <WifiOff className="w-4 h-4 text-red-500" />
            )}
            <span className="text-muted-foreground">Real-time</span>
            <Badge variant={isConnected ? 'default' : 'destructive'} className="text-xs">
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <RefreshCw className="w-3 h-3" />
            <span>{updateCount} updates</span>
            <span>•</span>
            <span>{formatTimeAgo(lastUpdate)}</span>
          </div>
        </div>
        
        {!isConnected && (
          <div className="mt-2 text-xs text-red-600">
            No recent updates detected. Try refreshing if you're not seeing changes.
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RealTimeStatus;
