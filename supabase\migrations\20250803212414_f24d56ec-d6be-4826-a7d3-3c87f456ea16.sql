-- Create enum for game status
CREATE TYPE game_status AS ENUM ('waiting', 'mafia_reveal', 'day_discussion', 'day_voting', 'night', 'final_discussion', 'ended');

-- Create enum for player roles
CREATE TYPE player_role AS ENUM ('mafia', 'civilian', 'detective', 'doctor');

-- Create games table
CREATE TABLE public.games (
  id TEXT PRIMARY KEY,
  host_id TEXT NOT NULL, -- Changed from UUID with foreign key to TEXT to support guest users
  status game_status NOT NULL DEFAULT 'waiting',
  max_players INTEGER NOT NULL DEFAULT 8,
  mafia_count INTEGER NOT NULL DEFAULT 2,
  discussion_time INTEGER NOT NULL DEFAULT 300,
  is_public BOOLEAN NOT NULL DEFAULT true,
  game_name TEXT,
  current_phase_end TIMESTAMP WITH TIME ZONE,
  winner TEXT, -- 'mafia' or 'civilian'
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create players table
CREATE TABLE public.players (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  game_id TEXT NOT NULL REFERENCES public.games(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL, -- Changed from UUID with foreign key to TEXT to support guest users
  name TEXT NOT NULL,
  role player_role,
  is_alive BOOLEAN NOT NULL DEFAULT true,
  is_ready BOOLEAN NOT NULL DEFAULT false,
  is_camera_on BOOLEAN NOT NULL DEFAULT true,
  is_mic_on BOOLEAN NOT NULL DEFAULT true,
  joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create votes table
CREATE TABLE public.votes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  game_id TEXT NOT NULL REFERENCES public.games(id) ON DELETE CASCADE,
  voter_id UUID NOT NULL REFERENCES public.players(id) ON DELETE CASCADE,
  target_id UUID REFERENCES public.players(id) ON DELETE CASCADE,
  vote_type TEXT NOT NULL, -- 'eliminate' or 'mafia_kill'
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(game_id, voter_id, vote_type)
);

-- Create game_messages table for chat
CREATE TABLE public.game_messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  game_id TEXT NOT NULL REFERENCES public.games(id) ON DELETE CASCADE,
  player_id UUID NOT NULL REFERENCES public.players(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  is_mafia_chat BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.games ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.game_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for games
CREATE POLICY "Anyone can view games" ON public.games FOR SELECT USING (true);
CREATE POLICY "Anyone can create games" ON public.games FOR INSERT WITH CHECK (true);
CREATE POLICY "Game hosts can update their games" ON public.games FOR UPDATE USING (host_id = auth.uid()::text OR host_id LIKE 'guest_%');

-- RLS Policies for players
CREATE POLICY "Anyone can view players" ON public.players FOR SELECT USING (true);
CREATE POLICY "Anyone can join games" ON public.players FOR INSERT WITH CHECK (true);
CREATE POLICY "Players can update themselves" ON public.players FOR UPDATE USING (user_id = auth.uid()::text OR user_id LIKE 'guest_%');
CREATE POLICY "Game hosts can manage players" ON public.players FOR ALL USING (
  EXISTS (SELECT 1 FROM public.games WHERE games.id = players.game_id AND (games.host_id = auth.uid()::text OR games.host_id LIKE 'guest_%'))
);

-- RLS Policies for votes
CREATE POLICY "Players can view votes in their game" ON public.votes FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.players WHERE players.id = votes.voter_id AND (players.user_id = auth.uid()::text OR players.user_id LIKE 'guest_%'))
  OR EXISTS (SELECT 1 FROM public.players WHERE players.game_id = votes.game_id AND (players.user_id = auth.uid()::text OR players.user_id LIKE 'guest_%'))
);
CREATE POLICY "Players can vote" ON public.votes FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM public.players WHERE players.id = votes.voter_id AND (players.user_id = auth.uid()::text OR players.user_id LIKE 'guest_%'))
);
CREATE POLICY "Players can update their votes" ON public.votes FOR UPDATE USING (
  EXISTS (SELECT 1 FROM public.players WHERE players.id = votes.voter_id AND (players.user_id = auth.uid()::text OR players.user_id LIKE 'guest_%'))
);

-- RLS Policies for messages
CREATE POLICY "Players can view messages in their game" ON public.game_messages FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.players WHERE players.game_id = game_messages.game_id AND (players.user_id = auth.uid()::text OR players.user_id LIKE 'guest_%'))
  AND (
    is_mafia_chat = false
    OR EXISTS (SELECT 1 FROM public.players WHERE players.game_id = game_messages.game_id AND (players.user_id = auth.uid()::text OR players.user_id LIKE 'guest_%') AND players.role = 'mafia')
  )
);
CREATE POLICY "Players can send messages" ON public.game_messages FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM public.players WHERE players.id = game_messages.player_id AND (players.user_id = auth.uid()::text OR players.user_id LIKE 'guest_%'))
);

-- Enable realtime
ALTER TABLE public.games REPLICA IDENTITY FULL;
ALTER TABLE public.players REPLICA IDENTITY FULL;
ALTER TABLE public.votes REPLICA IDENTITY FULL;
ALTER TABLE public.game_messages REPLICA IDENTITY FULL;

-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.games;
ALTER PUBLICATION supabase_realtime ADD TABLE public.players;
ALTER PUBLICATION supabase_realtime ADD TABLE public.votes;
ALTER PUBLICATION supabase_realtime ADD TABLE public.game_messages;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_games_updated_at
  BEFORE UPDATE ON public.games
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();