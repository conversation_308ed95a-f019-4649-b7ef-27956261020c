# 🎮 Complete Mafia Game Flow Testing Guide

## 🎯 **Overview**
Now that mock game creation and joining works, let's test the complete game flow from start to finish.

## 🧪 **Phase 1: Multi-Player Setup (3+ Players)**

### **Step 1: Create Mock Game**
1. **Open** http://localhost:8080
2. **Authenticate** as guest (e.g., "Host Player")
3. **Create mock game** using Mock Game Creator
4. **Note the Game ID** (e.g., MOCK_123456)

### **Step 2: Join Additional Players**
1. **Use Mock Game Debugger** to copy game ID
2. **Open 2 more browser tabs** (for 3 total players)
3. **In each new tab:**
   - Navigate to http://localhost:8080
   - Authe<PERSON><PERSON> as guest ("Player2", "Player3")
   - Join game using copied game ID
4. **Verify** all 3 players appear in waiting room

### **Step 3: Start Game**
1. **All players mark ready** in waiting room
2. **Host clicks "Start Game"**
3. **Expected:** Automatic navigation to `/game/{gameId}`
4. **Check console** for role assignment logs

## 🎮 **Phase 2: Game Phase Testing**

### **Mafia Reveal Phase (5 seconds)**
**What to Test:**
- ✅ Timer shows 5-second countdown
- ✅ Mafia players see "You are MAFIA" message
- ✅ Mafia players can see other mafia members
- ✅ Civilian players see "You are CIVILIAN" message
- ✅ Automatic progression to discussion phase

**Expected Console Logs:**
```
✅ Mock game started! Roles assigned
✅ Game phase: mafia_reveal
✅ Player roles: [Player1: mafia, Player2: civilian, Player3: civilian]
✅ Phase will auto-progress in 5 seconds
```

### **Discussion Phase (2 minutes)**
**What to Test:**
- ✅ Timer shows 120-second countdown
- ✅ All players can see each other
- ✅ Chat functionality works
- ✅ Players can type and send messages
- ✅ Messages appear in chat log
- ✅ Automatic progression to voting phase

**Testing Steps:**
1. **Type messages** in chat box
2. **Send messages** and verify they appear
3. **Switch between tabs** to verify all players see messages
4. **Wait for timer** or use Game Phase Tester to skip

### **Voting Phase (60 seconds)**
**What to Test:**
- ✅ Voting interface appears
- ✅ Can click on players to select for elimination
- ✅ Cannot vote for self (button disabled)
- ✅ Vote submission shows toast confirmation
- ✅ Votes appear in vote log
- ✅ Automatic progression after timer

**Testing Steps:**
1. **Click on a player** to select for elimination
2. **Submit vote** and verify toast appears
3. **Check vote log** for your vote
4. **All players vote** for same or different targets
5. **Wait for elimination** and phase progression

### **Night Phase (30 seconds)**
**What to Test:**
- ✅ Mafia players can vote to eliminate civilian
- ✅ Civilian players see "Night phase - you cannot act"
- ✅ Mafia-only chat functionality (if implemented)
- ✅ Automatic progression to next day or win condition

**Testing Steps:**
1. **Mafia players** vote to eliminate a civilian
2. **Civilian players** verify they cannot act
3. **Wait for phase progression**

### **Elimination & Observer Mode**
**What to Test:**
- ✅ Eliminated player's role is revealed
- ✅ Eliminated player marked as "dead" in player list
- ✅ Eliminated player can still see game progress
- ✅ Eliminated player cannot vote or participate
- ✅ Game continues with remaining players

### **Win Condition Testing**
**What to Test:**
- ✅ Game automatically checks win conditions
- ✅ Civilian victory: All mafia eliminated
- ✅ Mafia victory: Mafia equals/outnumbers civilians
- ✅ Final discussion phase (60 seconds)
- ✅ Game end screen shows winner

## 🛠 **Testing Tools Available**

### **Game Phase Tester** (Appears during mock games)
- **Real-time game state monitoring**
- **Force phase changes** for testing
- **Player status overview**
- **Timer and progress tracking**
- **Quick navigation buttons**

### **Browser Console Monitoring**
- **Role assignment logs**
- **Phase progression messages**
- **Vote and message confirmations**
- **Win condition checks**

### **Mock Game Debugger**
- **Complete localStorage inspection**
- **Game state verification**
- **Player data monitoring**

## 🧪 **Specific Test Scenarios**

### **Scenario 1: Civilian Victory**
1. **Start game** with 3 players (1 mafia, 2 civilians)
2. **During voting phase:** All players vote to eliminate the mafia
3. **Expected:** Civilian victory, final discussion, game end

### **Scenario 2: Mafia Victory**
1. **Start game** with 3 players (1 mafia, 2 civilians)
2. **During night phase:** Mafia eliminates 1 civilian
3. **Result:** 1 mafia, 1 civilian (mafia wins)
4. **Expected:** Mafia victory, final discussion, game end

### **Scenario 3: Extended Game**
1. **Start game** with 4+ players
2. **Play through multiple day/night cycles**
3. **Test elimination mechanics**
4. **Verify observer mode for eliminated players**

## 🔍 **Debugging Steps**

### **If Game Doesn't Start:**
1. **Check console** for error messages
2. **Verify all players are ready**
3. **Use Game Phase Tester** to force phase change
4. **Check Mock Game Debugger** for game state

### **If Phases Don't Progress:**
1. **Check timer functionality**
2. **Look for JavaScript errors**
3. **Use Game Phase Tester** to manually progress
4. **Verify localStorage data integrity**

### **If Voting Doesn't Work:**
1. **Check console** for vote submission logs
2. **Verify player is alive and can vote**
3. **Check localStorage** for vote data
4. **Test with different players**

## ✅ **Success Criteria**

### **Complete Game Flow:**
- ✅ 3+ players can join mock game
- ✅ Game starts with proper role assignment
- ✅ All phases progress automatically with timers
- ✅ Voting and elimination mechanics work
- ✅ Win conditions trigger correctly
- ✅ Final discussion and game end work
- ✅ Observer mode functions for eliminated players

### **User Experience:**
- ✅ Clear phase indicators and timers
- ✅ Intuitive voting interface
- ✅ Proper role visibility (mafia see each other)
- ✅ Chat functionality works smoothly
- ✅ Game state updates in real-time across tabs

## 🚀 **Next Steps After Testing**

Once mock game flow works perfectly:
1. **Fix database schema** for real games
2. **Implement video chat integration**
3. **Add advanced features** (detective, doctor roles)
4. **Optimize performance** and user experience
5. **Deploy and test** in production environment

## 📋 **Test Checklist**

- [ ] Mock game creation works
- [ ] Multi-player joining works
- [ ] Game start and role assignment works
- [ ] Mafia reveal phase works (5 seconds)
- [ ] Discussion phase works (2 minutes)
- [ ] Voting phase works (60 seconds)
- [ ] Night phase works (30 seconds)
- [ ] Elimination mechanics work
- [ ] Observer mode works
- [ ] Win condition detection works
- [ ] Final discussion works (60 seconds)
- [ ] Game end screen works
- [ ] All phases progress automatically
- [ ] Chat functionality works
- [ ] Game Phase Tester works
- [ ] Multi-tab synchronization works

**Test this complete flow now and report any issues!** 🎮
