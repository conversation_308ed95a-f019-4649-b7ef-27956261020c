@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for Mafia game */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.8);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.mafia-glow {
  animation: pulse-glow 2s infinite;
}

.fade-in {
  animation: fade-in 0.3s ease-out;
}

.slide-in {
  animation: slide-in 0.3s ease-out;
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease;
}

/* Video chat specific styles */
.video-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.video-player {
  position: relative;
  aspect-ratio: 16/9;
  border-radius: 0.5rem;
  overflow: hidden;
  background: #1a1a1a;
}

.video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 0.5rem;
  color: white;
}

/* Game phase indicators */
.phase-indicator {
  position: relative;
  overflow: hidden;
}

.phase-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Gaming-inspired Mafia theme design system - Dark aesthetic with purple/red accents */

@layer base {
  :root {
    /* Dark gaming background */
    --background: 240 10% 8%;
    --foreground: 210 40% 95%;

    /* Card styling with subtle transparency */
    --card: 240 8% 12%;
    --card-foreground: 210 40% 95%;

    /* Popover styling */
    --popover: 240 8% 12%;
    --popover-foreground: 210 40% 95%;

    /* Mafia-inspired primary colors */
    --primary: 350 80% 55%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 350 80% 65%;

    /* Secondary with purple accent */
    --secondary: 260 30% 20%;
    --secondary-foreground: 210 40% 95%;

    /* Muted elements */
    --muted: 240 5% 15%;
    --muted-foreground: 215 15% 65%;

    /* Accent with gaming purple */
    --accent: 260 60% 50%;
    --accent-foreground: 210 40% 98%;

    /* Destructive red for eliminations */
    --destructive: 0 75% 55%;
    --destructive-foreground: 210 40% 98%;

    /* Civilian blue theme */
    --civilian: 210 80% 55%;
    --civilian-foreground: 210 40% 98%;

    /* Borders and inputs */
    --border: 240 5% 18%;
    --input: 240 5% 18%;
    --ring: 350 80% 55%;

    /* Gaming gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--accent)));
    --gradient-background: linear-gradient(180deg, hsl(var(--background)), hsl(240 8% 6%));
    
    /* Gaming shadows with glow effects */
    --shadow-primary: 0 10px 30px -10px hsl(var(--primary) / 0.4);
    --shadow-secondary: 0 8px 25px -8px hsl(var(--accent) / 0.3);
    --shadow-glow: 0 0 40px hsl(var(--primary) / 0.2);

    /* Smooth transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: var(--gradient-background);
    min-height: 100vh;
  }
}

@layer components {
  /* Gaming button variants */
  .btn-mafia {
    @apply bg-gradient-to-r from-primary to-primary-glow text-primary-foreground shadow-lg;
    box-shadow: var(--shadow-primary);
    transition: var(--transition-smooth);
  }
  
  .btn-mafia:hover {
    @apply scale-105;
    box-shadow: var(--shadow-glow);
  }
  
  .btn-civilian {
    @apply bg-gradient-to-r from-civilian via-cyan-500 to-blue-500 text-civilian-foreground shadow-lg;
    box-shadow: var(--shadow-secondary);
    transition: var(--transition-smooth);
  }
  
  .btn-civilian:hover {
    @apply scale-105;
  }
  
  .btn-ghost-glow {
    @apply bg-transparent border border-primary/30 text-primary hover:bg-primary/10;
    transition: var(--transition-smooth);
  }
  
  .btn-ghost-glow:hover {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }

  /* Game card styles */
  .game-card {
    @apply bg-card/80 backdrop-blur-sm border border-border/50 rounded-lg p-6;
    box-shadow: var(--shadow-secondary);
    transition: var(--transition-smooth);
  }
  
  .game-card:hover {
    @apply border-primary/30;
    box-shadow: 0 8px 32px hsl(var(--primary) / 0.15);
  }

  /* Pulse animation for active states */
  .pulse-primary {
    animation: pulse-primary 2s infinite;
  }
  
  @keyframes pulse-primary {
    0%, 100% {
      box-shadow: 0 0 0 0 hsl(var(--primary) / 0.7);
    }
    50% {
      box-shadow: 0 0 0 10px hsl(var(--primary) / 0);
    }
  }
  
  /* Gaming text effects */
  .text-glow {
    text-shadow: 0 0 10px hsl(var(--primary) / 0.5);
  }
  
  .text-glow-secondary {
    text-shadow: 0 0 10px hsl(var(--accent) / 0.5);
  }
}