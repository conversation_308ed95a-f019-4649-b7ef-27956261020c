import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';

const MockGameCreator: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [gameName, setGameName] = useState('Mock Test Game');
  const [isCreating, setIsCreating] = useState(false);

  const createMockGame = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "No user found. Please authenticate first.",
        variant: "destructive"
      });
      return;
    }

    setIsCreating(true);

    try {
      // Generate a mock game ID
      const gameId = `MOCK_${Date.now().toString().slice(-6)}`;
      
      console.log('Creating mock game (no database):', {
        gameId,
        hostId: user.id,
        gameName,
        userType: user.id.startsWith('guest_') ? 'guest' : 'authenticated'
      });

      // Simulate game creation delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Store mock game data in localStorage for testing
      const mockGameData = {
        id: gameId,
        host_id: user.id,
        game_name: gameName,
        max_players: 3,
        mafia_count: 1,
        discussion_time: 60,
        is_public: false,
        status: 'waiting',
        created_at: new Date().toISOString()
      };

      localStorage.setItem(`mock_game_${gameId}`, JSON.stringify(mockGameData));
      console.log('Stored mock game in localStorage with key:', `mock_game_${gameId}`);

      // Store mock player data
      const mockPlayerData = {
        id: `player_${Date.now()}`,
        game_id: gameId,
        user_id: user.id,
        name: user.user_metadata?.name || 'Host',
        role: null,
        is_alive: true,
        is_ready: false,
        is_camera_on: true,
        is_mic_on: true,
        joined_at: new Date().toISOString()
      };

      const playerKey = `mock_player_${gameId}_${user.id}`;
      localStorage.setItem(playerKey, JSON.stringify(mockPlayerData));
      console.log('Stored mock player in localStorage with key:', playerKey);

      console.log('Mock game created successfully:', mockGameData);
      console.log('Mock player created:', mockPlayerData);

      // Verify storage
      console.log('Verification - Game in storage:', localStorage.getItem(`mock_game_${gameId}`) !== null);
      console.log('Verification - Player in storage:', localStorage.getItem(playerKey) !== null);

      toast({
        title: "Mock Game Created!",
        description: `Game ID: ${gameId} (stored locally for testing)`,
      });

      // Navigate to waiting room
      navigate(`/waiting/${gameId}`);

    } catch (error: any) {
      console.error('Error in createMockGame:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create mock game",
        variant: "destructive"
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Mock Game Creator</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="mock-game-name">Game Name</Label>
          <Input
            id="mock-game-name"
            value={gameName}
            onChange={(e) => setGameName(e.target.value)}
            placeholder="Enter game name"
          />
        </div>

        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
          <p><strong>⚠️ Mock Mode:</strong></p>
          <p>• Creates game in localStorage only</p>
          <p>• No database interaction</p>
          <p>• Tests UI and navigation flow</p>
          <p>• Use this if database creation fails</p>
        </div>

        <div className="p-3 bg-muted rounded-lg text-sm">
          <p><strong>Settings:</strong></p>
          <p>• 3 players maximum</p>
          <p>• 1 mafia member</p>
          <p>• 60 seconds discussion time</p>
          <p>• Private game</p>
        </div>

        <Button 
          onClick={createMockGame}
          disabled={!gameName.trim() || isCreating}
          className="w-full"
          variant="outline"
        >
          {isCreating ? 'Creating Mock Game...' : 'Create Mock Game (No DB)'}
        </Button>

        {user && (
          <div className="p-2 bg-blue-50 rounded text-xs">
            <p><strong>User:</strong> {user.user_metadata?.name || user.email || 'Guest'}</p>
            <p><strong>ID:</strong> {user.id}</p>
            <p><strong>Type:</strong> {user.id.startsWith('guest_') ? 'Guest' : 'Authenticated'}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MockGameCreator;
