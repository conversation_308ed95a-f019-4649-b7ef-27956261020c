import { useState, useEffect, createContext, useContext } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signOut: () => Promise<void>;
  signInAnonymously: (name: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for existing guest session in sessionStorage (tab-specific)
  useEffect(() => {
    // First check sessionStorage (tab-specific)
    const sessionGuestUser = sessionStorage.getItem('mafia_guest_user');
    console.log('Checking for tab-specific guest session:', sessionGuestUser ? 'Found' : 'Not found');

    if (sessionGuestUser) {
      try {
        const parsedUser = JSON.parse(sessionGuestUser);
        console.log('Restored tab-specific guest user:', parsedUser.user_metadata?.name);
        setUser(parsedUser);
        setLoading(false);
        return;
      } catch (error) {
        console.error('Failed to parse tab-specific guest user:', error);
        sessionStorage.removeItem('mafia_guest_user');
      }
    }

    // Fallback to localStorage for backward compatibility
    const localGuestUser = localStorage.getItem('mafia_guest_user');
    console.log('Checking for localStorage guest session:', localGuestUser ? 'Found' : 'Not found');

    if (localGuestUser) {
      try {
        const parsedUser = JSON.parse(localGuestUser);
        console.log('Restored localStorage guest user:', parsedUser.user_metadata?.name);
        // Move to sessionStorage for this tab
        sessionStorage.setItem('mafia_guest_user', localGuestUser);
        localStorage.removeItem('mafia_guest_user');
        setUser(parsedUser);
        setLoading(false);
        return;
      } catch (error) {
        console.error('Failed to parse localStorage guest user:', error);
        localStorage.removeItem('mafia_guest_user');
      }
    }
  }, []);

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state change:', event, session?.user ? 'User authenticated' : 'No user');
        if (session?.user) {
          setUser(session.user);
          // Clear any guest session when real auth succeeds
          localStorage.removeItem('mafia_guest_user');
        } else if (!localStorage.getItem('mafia_guest_user')) {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) throw error;
  };

  const signUp = async (email: string, password: string, name: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: { name }
      }
    });
    if (error) throw error;
  };

  const signOut = async () => {
    console.log('Signing out user...');

    // Clear guest session from both storages
    sessionStorage.removeItem('mafia_guest_user');
    localStorage.removeItem('mafia_guest_user');

    // Sign out from Supabase if authenticated
    const { error } = await supabase.auth.signOut();
    if (error && !error.message.includes('not authenticated')) {
      throw error;
    }

    setUser(null);
    setLoading(false);

    console.log('User signed out successfully');
  };

  const signInAnonymously = async (name: string) => {
    try {
      // Try anonymous authentication first
      const { error } = await supabase.auth.signInAnonymously({
        options: {
          data: { name }
        }
      });

      if (error) {
        console.warn('Anonymous auth failed, using fallback:', error.message);
        // Fallback: create a guest user session
        const guestUser = {
          id: `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          email: `guest_${Date.now()}@temp.local`,
          user_metadata: { name },
          app_metadata: {},
          aud: 'authenticated',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        } as any;

        // Store guest session in sessionStorage (tab-specific)
        console.log('Creating tab-specific guest user session:', name);
        sessionStorage.setItem('mafia_guest_user', JSON.stringify(guestUser));
        setUser(guestUser);
        return;
      }
    } catch (error) {
      console.error('Authentication error:', error);
      // Even if Supabase fails completely, create a guest session
      const guestUser = {
        id: `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        email: `guest_${Date.now()}@temp.local`,
        user_metadata: { name },
        app_metadata: {},
        aud: 'authenticated',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      } as any;

      localStorage.setItem('mafia_guest_user', JSON.stringify(guestUser));
      setUser(guestUser);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      signIn,
      signUp,
      signOut,
      signInAnonymously
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};