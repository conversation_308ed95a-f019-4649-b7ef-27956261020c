import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Crown, Skull, Trophy, Users, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Game, Player } from '@/hooks/useGame';

interface GameEndScreenProps {
  game: Game;
  players: Player[];
  currentPlayer: Player | null;
}

const GameEndScreen: React.FC<GameEndScreenProps> = ({ game, players, currentPlayer }) => {
  const navigate = useNavigate();
  
  const mafiaPlayers = players.filter(p => p.role === 'mafia');
  const civilianPlayers = players.filter(p => p.role === 'civilian');
  const alivePlayers = players.filter(p => p.is_alive);
  const deadPlayers = players.filter(p => !p.is_alive);
  
  const isWinner = () => {
    if (!currentPlayer) return false;
    
    if (game.winner === 'mafia' && currentPlayer.role === 'mafia') {
      return true;
    }
    
    if (game.winner === 'civilians' && currentPlayer.role !== 'mafia') {
      return true;
    }
    
    return false;
  };

  const getWinReason = () => {
    if (game.winner === 'civilians') {
      return 'All mafia members have been eliminated!';
    } else if (game.winner === 'mafia') {
      const aliveMafia = alivePlayers.filter(p => p.role === 'mafia');
      const aliveCivilians = alivePlayers.filter(p => p.role !== 'mafia');
      
      if (aliveMafia.length >= aliveCivilians.length) {
        return 'Mafia equals or outnumbers the civilians!';
      }
    }
    return 'Game ended';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted to-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        
        {/* Game Over Header */}
        <Card className="text-center">
          <CardHeader>
            <CardTitle className="text-4xl font-bold flex items-center justify-center gap-3">
              <Trophy className={`w-10 h-10 ${game.winner === 'mafia' ? 'text-destructive' : 'text-primary'}`} />
              Game Over
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className={`text-2xl font-semibold ${game.winner === 'mafia' ? 'text-destructive' : 'text-primary'}`}>
                {game.winner === 'mafia' ? 'Mafia Wins!' : 'Civilians Win!'}
              </div>
              <p className="text-muted-foreground">
                {getWinReason()}
              </p>
              
              {/* Personal Result */}
              {currentPlayer && (
                <div className={`p-4 rounded-lg ${isWinner() ? 'bg-green-500/10 border border-green-500/20' : 'bg-red-500/10 border border-red-500/20'}`}>
                  <div className="flex items-center justify-center gap-2">
                    {isWinner() ? (
                      <>
                        <Trophy className="w-5 h-5 text-green-500" />
                        <span className="font-medium text-green-500">You Won!</span>
                      </>
                    ) : (
                      <>
                        <Skull className="w-5 h-5 text-red-500" />
                        <span className="font-medium text-red-500">You Lost</span>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Team Breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          {/* Mafia Team */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <Skull className="w-5 h-5" />
                Mafia Team ({mafiaPlayers.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mafiaPlayers.map((player) => (
                  <div
                    key={player.id}
                    className={`p-3 rounded-lg border ${player.is_alive ? 'bg-destructive/10 border-destructive/20' : 'bg-muted/50 opacity-60'}`}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarFallback>
                          {player.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className={`font-medium ${!player.is_alive ? 'line-through' : ''}`}>
                          {player.name}
                          {player.id === currentPlayer?.id && ' (You)'}
                        </div>
                        <Badge 
                          variant={player.is_alive ? "destructive" : "outline"}
                          className="text-xs mt-1"
                        >
                          {player.is_alive ? "Survived" : "Eliminated"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Civilian Team */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-primary">
                <Users className="w-5 h-5" />
                Civilian Team ({civilianPlayers.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {civilianPlayers.map((player) => (
                  <div
                    key={player.id}
                    className={`p-3 rounded-lg border ${player.is_alive ? 'bg-primary/10 border-primary/20' : 'bg-muted/50 opacity-60'}`}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarFallback>
                          {player.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className={`font-medium ${!player.is_alive ? 'line-through' : ''}`}>
                          {player.name}
                          {player.id === currentPlayer?.id && ' (You)'}
                        </div>
                        <Badge 
                          variant={player.is_alive ? "default" : "outline"}
                          className="text-xs mt-1"
                        >
                          {player.is_alive ? "Survived" : "Eliminated"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Game Statistics */}
        <Card>
          <CardHeader>
            <CardTitle>Game Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="p-3 rounded-lg bg-muted/50">
                <div className="text-2xl font-bold">{players.length}</div>
                <div className="text-sm text-muted-foreground">Total Players</div>
              </div>
              <div className="p-3 rounded-lg bg-destructive/10">
                <div className="text-2xl font-bold text-destructive">{mafiaPlayers.length}</div>
                <div className="text-sm text-muted-foreground">Mafia Members</div>
              </div>
              <div className="p-3 rounded-lg bg-primary/10">
                <div className="text-2xl font-bold text-primary">{civilianPlayers.length}</div>
                <div className="text-sm text-muted-foreground">Civilians</div>
              </div>
              <div className="p-3 rounded-lg bg-muted/50">
                <div className="text-2xl font-bold">{deadPlayers.length}</div>
                <div className="text-sm text-muted-foreground">Eliminated</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardContent className="p-6 text-center">
            <Button 
              onClick={() => navigate('/')}
              size="lg"
              className="w-full md:w-auto"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default GameEndScreen;
