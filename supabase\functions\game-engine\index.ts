import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const { gameId, action } = await req.json();

    // Get game data
    const { data: game, error: gameError } = await supabase
      .from('games')
      .select('*')
      .eq('id', gameId)
      .single();

    if (gameError) throw gameError;

    const { data: players, error: playersError } = await supabase
      .from('players')
      .select('*')
      .eq('game_id', gameId);

    if (playersError) throw playersError;

    const { data: votes, error: votesError } = await supabase
      .from('votes')
      .select('*')
      .eq('game_id', gameId);

    if (votesError) throw votesError;

    let response = { success: true, message: '' };

    switch (action) {
      case 'advance_phase':
        response = await advancePhase(supabase, game, players, votes);
        break;
      case 'check_win_condition':
        response = await checkWinCondition(supabase, game, players);
        break;
      default:
        throw new Error('Invalid action');
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Game engine error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function advancePhase(supabase: any, game: any, players: any[], votes: any[]) {
  const alivePlayers = players.filter(p => p.is_alive);
  const mafiaPlayers = alivePlayers.filter(p => p.role === 'mafia');

  let nextStatus = game.status;
  let phaseEnd = null;
  let eliminatedPlayer = null;

  switch (game.status) {
    case 'waiting':
      // Start with mafia reveal phase
      nextStatus = 'mafia_reveal';
      phaseEnd = new Date(Date.now() + 5000); // 5 seconds for mafia reveal
      break;

    case 'mafia_reveal':
      // Move to first discussion phase
      nextStatus = 'day_discussion';
      phaseEnd = new Date(Date.now() + game.discussion_time * 1000);
      break;

    case 'day_discussion':
      nextStatus = 'day_voting';
      phaseEnd = new Date(Date.now() + 60000); // 1 minute for voting
      break;

    case 'day_voting':
      // Process elimination votes
      const eliminationVotes = votes.filter(v => v.vote_type === 'eliminate');
      eliminatedPlayer = await processVotes(supabase, eliminationVotes, players);

      if (eliminatedPlayer) {
        await supabase
          .from('players')
          .update({ is_alive: false })
          .eq('id', eliminatedPlayer.id);
      }

      // Clear votes
      await supabase
        .from('votes')
        .delete()
        .eq('game_id', game.id);

      // Check if game should end or continue
      const alivePlayers = players.filter(p => p.is_alive && p.id !== eliminatedPlayer?.id);
      const aliveMafia = alivePlayers.filter(p => p.role === 'mafia');
      const aliveCivilians = alivePlayers.filter(p => p.role !== 'mafia');

      if (aliveMafia.length === 0 || aliveMafia.length >= aliveCivilians.length) {
        nextStatus = 'final_discussion';
        phaseEnd = new Date(Date.now() + 60000); // 1 minute final discussion
      } else {
        nextStatus = 'day_discussion';
        phaseEnd = new Date(Date.now() + game.discussion_time * 1000);
      }
      break;

    case 'final_discussion':
      nextStatus = 'ended';
      phaseEnd = null;
      break;
  }

  // Update game status
  await supabase
    .from('games')
    .update({
      status: nextStatus,
      current_phase_end: phaseEnd?.toISOString()
    })
    .eq('id', game.id);

  return {
    success: true,
    message: `Phase advanced to ${nextStatus}`,
    eliminatedPlayer,
    nextPhase: nextStatus
  };
}

async function processVotes(supabase: any, votes: any[], players: any[]) {
  if (votes.length === 0) return null;

  // Count votes for each target
  const voteCounts: Record<string, number> = {};
  votes.forEach(vote => {
    if (vote.target_id) {
      voteCounts[vote.target_id] = (voteCounts[vote.target_id] || 0) + 1;
    }
  });

  // Find player with most votes
  let maxVotes = 0;
  let eliminatedPlayerId = null;

  Object.entries(voteCounts).forEach(([playerId, count]) => {
    if (count > maxVotes) {
      maxVotes = count;
      eliminatedPlayerId = playerId;
    }
  });

  return eliminatedPlayerId ? players.find(p => p.id === eliminatedPlayerId) : null;
}

async function checkWinCondition(supabase: any, game: any, players: any[]) {
  const alivePlayers = players.filter(p => p.is_alive);
  const aliveMafia = alivePlayers.filter(p => p.role === 'mafia');
  const aliveCivilians = alivePlayers.filter(p => p.role !== 'mafia');

  let winner = null;

  if (aliveMafia.length === 0) {
    winner = 'civilians';
  } else if (aliveMafia.length >= aliveCivilians.length) {
    winner = 'mafia';
  }

  if (winner) {
    await supabase
      .from('games')
      .update({
        status: 'ended',
        winner: winner
      })
      .eq('id', game.id);
  }

  return {
    success: true,
    gameEnded: !!winner,
    winner: winner,
    aliveMafia: aliveMafia.length,
    aliveCivilians: aliveCivilians.length
  };
}