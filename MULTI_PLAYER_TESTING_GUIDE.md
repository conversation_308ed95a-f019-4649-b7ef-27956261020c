# 🎮 Multi-Player Testing Guide - Tab-Specific Users

## 🎯 **Overview**

Fixed multi-tab testing! Each browser tab now has its own user session using sessionStorage. No more shared users across tabs!

## 🧪 **Step-by-Step Multi-Player Testing**

### **Method 1: Quick User Templates (Recommended)**

#### **Tab 1 - Host Player:**

1. **Open** <http://localhost:8080>
2. **If already logged in:** Click "Logout" in top-right corner
3. **In User Manager:** Click "Host Player" quick template
4. **Create mock game** using Mock Game Creator
5. **Copy game ID** from Mock Game Debugger

#### **Tab 2 - Player 2:**

1. **Open new tab** → <http://localhost:8080>
2. **In User Manager:** Click "Player 2" quick template
3. **Join game** using copied game ID
4. **Verify** you appear in waiting room

#### **Tab 3 - Player 3:**

1. **Open new tab** → <http://localhost:8080>
2. **In User Manager:** Click "Player 3" quick template
3. **Join game** using same game ID
4. **Verify** all 3 players appear in waiting room

#### **Start Game:**

1. **All players mark ready**
2. **Host starts game**
3. **Test complete game flow**

### **Method 2: Custom User Names**

#### **For Each Tab:**

1. **Open new tab** → <http://localhost:8080>
2. **If logged in:** Click "Logout" button
3. **In User Manager:** Enter custom name in "Create New User"
4. **Click "Create User"**
5. **Join game or create game as needed**

## 🛠 **User Management Features**

### **User Header (Top of Page):**

- **Shows current user** name and type (Guest/Auth)
- **Quick logout button** for easy user switching
- **User ID display** for debugging

### **User Manager Component:**

- **Current user info** with logout option
- **Create new user** with custom names
- **Quick user templates** for common testing scenarios
- **Advanced options** for clearing data

### **Quick User Templates:**

- **Host Player** - For creating games
- **Player 2, 3, 4** - For joining games
- **Tester** - General testing account
- **Observer** - For testing observer mode

## 🔧 **Troubleshooting**

### **If User Creation Fails:**

1. **Click "Logout"** first to clear current session
2. **Wait 1-2 seconds** before creating new user
3. **Try "Clear User Data Only"** if issues persist
4. **Use "Clear All Data & Refresh"** as last resort

### **If Authentication Gets Stuck:**

1. **Refresh the page** (F5)
2. **Clear browser cache** for the site
3. **Use incognito/private browsing** for clean testing
4. **Check browser console** for error messages

### **If Users Don't Sync Across Tabs:**

1. **Verify each tab has different user**
2. **Check User Header** shows correct user name
3. **Refresh tabs** if needed
4. **Use Mock Game Debugger** to verify game state

## ✅ **Complete Testing Workflow**

### **Phase 1: Setup (5 minutes)**

1. **Tab 1:** Create "Host Player" → Create mock game
2. **Tab 2:** Create "Player 2" → Join game
3. **Tab 3:** Create "Player 3" → Join game
4. **Verify:** All players in waiting room

### **Phase 2: Game Flow (10 minutes)**

1. **All ready** → Host starts game
2. **Mafia Reveal** → Check role assignments
3. **Discussion** → Test chat functionality
4. **Voting** → Test elimination mechanics
5. **Continue** → Play to win condition

### **Phase 3: Advanced Testing (5 minutes)**

1. **Test observer mode** for eliminated players
2. **Test both win scenarios** (civilian/mafia victory)
3. **Test final discussion** and game end
4. **Verify Game Phase Tester** functionality

## 🎮 **Testing Scenarios**

### **Scenario 1: Basic 3-Player Game**

- **1 Mafia, 2 Civilians**
- **Test standard game flow**
- **Verify role visibility**

### **Scenario 2: 4-Player Extended Game**

- **1 Mafia, 3 Civilians**
- **Test multiple day/night cycles**
- **Test elimination and observer mode**

### **Scenario 3: Quick Win Conditions**

- **Use Game Phase Tester** to force phases
- **Test both win scenarios quickly**
- **Verify final discussion works**

## 🔍 **Debugging Tools**

### **User Management:**

- **User Header** - Current user info
- **User Manager** - Create/switch users
- **Quick templates** - Fast user creation

### **Game Testing:**

- **Mock Game Debugger** - Game state inspection
- **Game Phase Tester** - Phase control and monitoring
- **Multi-Player Tester** - Player overview

### **Browser Tools:**

- **Console logs** - Detailed debugging info
- **Network tab** - Request monitoring
- **Application tab** - localStorage inspection

## 📋 **Quick Reference**

### **Essential Buttons:**

- **"Logout"** (top-right) - Quick user switching
- **"Host Player"** - Create game host
- **"Player 2/3"** - Join as additional players
- **"Copy ID"** - Get game ID for joining
- **"Join Game"** - Enter waiting room

### **Common Issues:**

- **"Already logged in"** → Click "Logout" first
- **"Game not found"** → Verify game ID is correct
- **"Can't create user"** → Clear user data and try again
- **"Players not syncing"** → Refresh tabs and rejoin

## 🚀 **Expected Results**

### **Successful Multi-Player Setup:**

- ✅ Each tab shows different user in header
- ✅ All players appear in same waiting room
- ✅ Ready status updates across tabs
- ✅ Game starts with proper role assignment
- ✅ All phases work with multiple players
- ✅ Win conditions trigger correctly

### **Console Log Success Pattern:**

```
Tab 1: ✅ User signed out → ✅ Host Player created → ✅ Mock game created
Tab 2: ✅ User signed out → ✅ Player 2 created → ✅ Joined game MOCK_123456
Tab 3: ✅ User signed out → ✅ Player 3 created → ✅ Joined game MOCK_123456
All: ✅ Game started → ✅ Roles assigned → ✅ Phases progressing
```

## 🎯 **Test This Now!**

1. **Open 3 browser tabs**
2. **Use quick user templates** to create different users
3. **Create and join mock game**
4. **Play through complete game flow**
5. **Report any issues** with specific error messages

The user management system should make multi-player testing much easier! 🎮
