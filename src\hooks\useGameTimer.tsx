import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Game } from './useGame';

export const useGameTimer = (game: Game | null) => {
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isActive, setIsActive] = useState(false);

  // Calculate time left based on current phase end
  const calculateTimeLeft = useCallback(() => {
    if (!game?.current_phase_end) return 0;
    
    const endTime = new Date(game.current_phase_end).getTime();
    const now = new Date().getTime();
    const difference = endTime - now;
    
    return Math.max(0, Math.floor(difference / 1000));
  }, [game?.current_phase_end]);

  // Update timer every second
  useEffect(() => {
    if (!game || game.status === 'waiting' || game.status === 'ended') {
      setIsActive(false);
      setTimeLeft(0);
      return;
    }

    setIsActive(true);
    setTimeLeft(calculateTimeLeft());

    const interval = setInterval(() => {
      const newTimeLeft = calculateTimeLeft();
      setTimeLeft(newTimeLeft);

      // Auto-advance phase when timer reaches 0
      if (newTimeLeft <= 0 && game.status !== 'ended') {
        advancePhase();
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [game, calculateTimeLeft]);

  // Advance to next phase
  const advancePhase = async () => {
    if (!game) return;

    try {
      await supabase.functions.invoke('game-engine', {
        body: { 
          gameId: game.id, 
          action: 'advance_phase' 
        }
      });
    } catch (error) {
      console.error('Error advancing phase:', error);
    }
  };

  // Check win conditions
  const checkWinCondition = async () => {
    if (!game) return;

    try {
      await supabase.functions.invoke('game-engine', {
        body: { 
          gameId: game.id, 
          action: 'check_win_condition' 
        }
      });
    } catch (error) {
      console.error('Error checking win condition:', error);
    }
  };

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get phase description
  const getPhaseDescription = () => {
    if (!game) return '';

    switch (game.status) {
      case 'mafia_reveal':
        return 'Mafia members are identifying each other';
      case 'day_discussion':
        return 'Discuss and find the mafia members';
      case 'day_voting':
        return 'Vote to eliminate a suspected mafia member';
      case 'final_discussion':
        return 'Final thoughts before the game ends';
      case 'ended':
        return 'Game has ended';
      default:
        return '';
    }
  };

  return {
    timeLeft,
    isActive,
    formatTime: () => formatTime(timeLeft),
    getPhaseDescription,
    advancePhase,
    checkWinCondition
  };
};
