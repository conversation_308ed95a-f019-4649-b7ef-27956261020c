import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { ArrowLeft, Users, Clock, Crown, Lock, Globe } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";

const CreateGame = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const [isCreating, setIsCreating] = useState(false);

  const [formData, setFormData] = useState({
    gameName: "",
    totalPlayers: 6,
    mafiaCount: 2,
    isPublic: true,
    discussionTime: 120, // seconds
  });

  const maxMafia = Math.floor((formData.totalPlayers - 1) / 2);

  const handleCreate = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please log in to create a game",
        variant: "destructive"
      });
      return;
    }

    if (!formData.gameName.trim()) {
      toast({
        title: "Game name required",
        description: "Please enter a name for your game",
        variant: "destructive"
      });
      return;
    }

    if (formData.totalPlayers < 3) {
      toast({
        title: "Invalid player count",
        description: "Minimum 3 players required",
        variant: "destructive"
      });
      return;
    }

    if (formData.mafiaCount >= formData.totalPlayers / 2) {
      toast({
        title: "Invalid mafia count",
        description: "Mafia must be less than half of total players",
        variant: "destructive"
      });
      return;
    }

    setIsCreating(true);

    try {
      // Test database connection first
      console.log('Testing database connection...');
      const { data: testData, error: testError } = await supabase
        .from('games')
        .select('count')
        .limit(1);

      if (testError) {
        console.error('Database connection test failed:', testError);
        throw new Error('Database connection failed: ' + testError.message);
      }

      console.log('Database connection successful');
      // Generate a random game ID
      const gameId = Math.random().toString(36).substring(2, 8).toUpperCase();

      console.log('Creating game with ID:', gameId, 'Host:', user.id);

      // Create game in database
      const { data, error } = await supabase
        .from('games')
        .insert({
          id: gameId,
          host_id: user.id,
          game_name: formData.gameName,
          max_players: formData.totalPlayers,
          mafia_count: formData.mafiaCount,
          discussion_time: formData.discussionTime,
          is_public: formData.isPublic,
          status: 'waiting'
        })
        .select();

      if (error) {
        console.error('Database error:', error);
        throw error;
      }

      console.log('Game created successfully:', data);

      // Automatically add the host as a player
      const hostName = user.user_metadata?.name || user.email || 'Host';
      const { error: playerError } = await supabase
        .from('players')
        .insert({
          game_id: gameId,
          user_id: user.id,
          name: hostName,
          is_ready: false
        });

      if (playerError) {
        console.error('Error adding host as player:', playerError);
        // Don't throw here, just log the error and continue
      }

      toast({
        title: "Game created!",
        description: `Game ID: ${gameId}`,
      });

      // Navigate to waiting room
      navigate(`/waiting/${gameId}`);
    } catch (error: any) {
      console.error('Error creating game:', error);

      // Show detailed error message
      const errorMessage = error.message || error.details || error.hint || 'Unknown error occurred';
      console.error('Detailed error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        user: user
      });

      toast({
        title: "Error Creating Game",
        description: `${errorMessage}. Check console for details.`,
        variant: "destructive"
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-2xl w-full space-y-6">
        {/* Back Button */}
        <Button 
          variant="ghost-glow" 
          onClick={() => navigate("/")}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Home
        </Button>

        <Card className="game-card">
          <CardHeader>
            <CardTitle className="text-2xl text-center text-glow">
              <Crown className="w-6 h-6 inline mr-2" />
              Create New Game
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Game Name */}
            <div className="space-y-2">
              <Label htmlFor="gameName">Game Name</Label>
              <Input
                id="gameName"
                placeholder="Enter game name..."
                value={formData.gameName}
                onChange={(e) => setFormData(prev => ({ ...prev, gameName: e.target.value }))}
                className="bg-input border-border"
              />
            </div>

            {/* Total Players */}
            <div className="space-y-4">
              <Label>Total Players: {formData.totalPlayers}</Label>
              <Slider
                value={[formData.totalPlayers]}
                onValueChange={(value) => {
                  const newTotal = value[0];
                  const newMaxMafia = Math.floor((newTotal - 1) / 2);
                  setFormData(prev => ({ 
                    ...prev, 
                    totalPlayers: newTotal,
                    mafiaCount: Math.min(prev.mafiaCount, newMaxMafia)
                  }));
                }}
                max={12}
                min={3}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>3 players</span>
                <span>12 players</span>
              </div>
            </div>

            {/* Mafia Count */}
            <div className="space-y-4">
              <Label>Mafia Members: {formData.mafiaCount}</Label>
              <Slider
                value={[formData.mafiaCount]}
                onValueChange={(value) => setFormData(prev => ({ ...prev, mafiaCount: value[0] }))}
                max={maxMafia}
                min={1}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>1 mafia</span>
                <span>{maxMafia} mafia max</span>
              </div>
            </div>

            {/* Discussion Time */}
            <div className="space-y-4">
              <Label>Discussion Time: {Math.floor(formData.discussionTime / 60)}:{(formData.discussionTime % 60).toString().padStart(2, '0')}</Label>
              <Slider
                value={[formData.discussionTime]}
                onValueChange={(value) => setFormData(prev => ({ ...prev, discussionTime: value[0] }))}
                max={180}
                min={30}
                step={15}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>30 seconds</span>
                <span>3 minutes</span>
              </div>
            </div>

            {/* Public/Private Toggle */}
            <div className="flex items-center justify-between p-4 rounded-lg bg-muted/30">
              <div className="flex items-center space-x-3">
                {formData.isPublic ? (
                  <Globe className="w-5 h-5 text-green-500" />
                ) : (
                  <Lock className="w-5 h-5 text-orange-500" />
                )}
                <div className="space-y-1">
                  <Label htmlFor="public-toggle">
                    {formData.isPublic ? "Public Game" : "Private Game"}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {formData.isPublic
                      ? "Others can discover and join your game"
                      : "Only players with the game ID can join"
                    }
                  </p>
                </div>
              </div>
              <Switch
                id="public-toggle"
                checked={formData.isPublic}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isPublic: checked }))}
              />
            </div>

            {/* Game Summary */}
            <div className="p-4 rounded-lg bg-primary/10 border border-primary/20">
              <h3 className="font-semibold mb-2 text-primary">Game Summary</h3>
              <div className="text-sm space-y-1 text-muted-foreground">
                <div className="flex justify-between">
                  <span>Players:</span>
                  <span>{formData.totalPlayers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Mafia:</span>
                  <span className="text-primary">{formData.mafiaCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>Civilians:</span>
                  <span className="text-civilian">{formData.totalPlayers - formData.mafiaCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>Discussion:</span>
                  <span>{Math.floor(formData.discussionTime / 60)}:{(formData.discussionTime % 60).toString().padStart(2, '0')}</span>
                </div>
                <div className="flex justify-between">
                  <span>Visibility:</span>
                  <span>{formData.isPublic ? "Public" : "Private"}</span>
                </div>
              </div>
            </div>

            {/* Create Button */}
            <Button
              variant="mafia"
              size="lg"
              className="w-full text-lg py-6"
              onClick={handleCreate}
              disabled={isCreating}
            >
              {isCreating ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              ) : (
                <Users className="w-5 h-5 mr-2" />
              )}
              {isCreating ? "Creating Game..." : "Create Game"}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CreateGame;