// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nyswlobwdhbjjzyygkty.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im55c3dsb2J3ZGhiamp6eXlna3R5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyNTU2MDksImV4cCI6MjA2OTgzMTYwOX0.SVjJzuKBMZA62nIzTPsZI7i371Qg_GmS2Mdsrs-_32A";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});