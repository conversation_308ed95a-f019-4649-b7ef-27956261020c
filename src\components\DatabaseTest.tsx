import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';

const DatabaseTest: React.FC = () => {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string, isError = false) => {
    const timestamp = new Date().toLocaleTimeString();
    const result = `[${timestamp}] ${isError ? '❌' : '✅'} ${message}`;
    setTestResults(prev => [...prev, result]);
    console.log(result);
  };

  const runDatabaseTests = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      // Test 1: Basic connection
      addResult('Testing database connection...');
      const { data: connectionTest, error: connectionError } = await supabase
        .from('games')
        .select('count')
        .limit(1);

      if (connectionError) {
        addResult(`Connection failed: ${connectionError.message}`, true);
        return;
      }
      addResult('Database connection successful');

      // Test 2: Check table structure
      addResult('Checking table structure...');
      const { data: tableInfo, error: tableError } = await supabase
        .from('games')
        .select('id, host_id, game_name, is_public')
        .limit(1);

      if (tableError) {
        addResult(`Table structure check failed: ${tableError.message}`, true);
        return;
      }
      addResult('Table structure is correct');

      // Test 3: Test game creation
      addResult('Testing game creation...');
      const testGameId = `TEST_${Date.now()}`;
      const { data: gameData, error: gameError } = await supabase
        .from('games')
        .insert({
          id: testGameId,
          host_id: user?.id || 'test_host',
          game_name: 'Test Game',
          max_players: 3,
          mafia_count: 1,
          discussion_time: 60,
          is_public: false,
          status: 'waiting'
        })
        .select();

      if (gameError) {
        addResult(`Game creation failed: ${gameError.message}`, true);
        return;
      }
      addResult('Game creation successful');

      // Test 4: Test player creation
      addResult('Testing player creation...');
      const { data: playerData, error: playerError } = await supabase
        .from('players')
        .insert({
          game_id: testGameId,
          user_id: user?.id || 'test_user',
          name: 'Test Player',
          is_ready: false
        })
        .select();

      if (playerError) {
        addResult(`Player creation failed: ${playerError.message}`, true);
      } else {
        addResult('Player creation successful');
      }

      // Test 5: Cleanup
      addResult('Cleaning up test data...');
      await supabase.from('games').delete().eq('id', testGameId);
      addResult('Cleanup completed');

    } catch (error: any) {
      addResult(`Unexpected error: ${error.message}`, true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Database Connection Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <Button 
            onClick={runDatabaseTests} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Running Tests...' : 'Run Database Tests'}
          </Button>
        </div>

        {user && (
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm">
              <strong>Current User:</strong> {user.user_metadata?.name || user.email || user.id}
            </p>
            <p className="text-sm">
              <strong>User ID:</strong> {user.id}
            </p>
            <Badge variant={user.id.startsWith('guest_') ? 'secondary' : 'default'}>
              {user.id.startsWith('guest_') ? 'Guest User' : 'Authenticated User'}
            </Badge>
          </div>
        )}

        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Test Results:</h3>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className={result.includes('❌') ? 'text-red-400' : 'text-green-400'}>
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DatabaseTest;
