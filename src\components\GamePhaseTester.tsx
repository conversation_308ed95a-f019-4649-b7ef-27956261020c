import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { Clock, Users, Crown, Skull, Eye, Zap } from 'lucide-react';

interface GamePhaseTestProps {
  gameId?: string;
}

const GamePhaseTester: React.FC<GamePhaseTestProps> = ({ gameId }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [gameState, setGameState] = useState<any>(null);
  const [players, setPlayers] = useState<any[]>([]);
  const [timeLeft, setTimeLeft] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);

  const loadGameState = () => {
    if (!gameId) return;

    // Load mock game data
    const gameData = localStorage.getItem(`mock_game_${gameId}`);
    if (gameData) {
      const parsed = JSON.parse(gameData);
      setGameState(parsed);

      // Calculate time left
      if (parsed.current_phase_end) {
        const endTime = new Date(parsed.current_phase_end).getTime();
        const now = new Date().getTime();
        const difference = Math.max(0, Math.floor((endTime - now) / 1000));
        setTimeLeft(difference);
      } else {
        setTimeLeft(0);
      }
    }

    // Load players
    const playerList: any[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(`mock_player_${gameId}_`)) {
        try {
          const playerData = localStorage.getItem(key);
          if (playerData) {
            playerList.push(JSON.parse(playerData));
          }
        } catch (error) {
          console.error('Error parsing player:', error);
        }
      }
    }
    setPlayers(playerList);
  };

  useEffect(() => {
    loadGameState();
    const interval = setInterval(loadGameState, 1000); // Refresh every second
    return () => clearInterval(interval);
  }, [gameId, refreshKey]);

  const refresh = () => {
    setRefreshKey(prev => prev + 1);
    toast({
      title: "Refreshed",
      description: "Game state refreshed",
    });
  };

  const forcePhaseChange = (newPhase: string) => {
    if (!gameId || !gameState) return;

    const now = new Date();
    let phaseDuration = 0;

    switch (newPhase) {
      case 'mafia_reveal':
        phaseDuration = 5 * 1000;
        break;
      case 'day_discussion':
        phaseDuration = 120 * 1000;
        break;
      case 'day_voting':
        phaseDuration = 60 * 1000;
        break;
      case 'night':
        phaseDuration = 30 * 1000;
        break;
      case 'final_discussion':
        phaseDuration = 60 * 1000;
        break;
      case 'ended':
        phaseDuration = 0;
        break;
    }

    const phaseEndTime = phaseDuration > 0 ? new Date(now.getTime() + phaseDuration) : null;

    const updatedGame = {
      ...gameState,
      status: newPhase,
      current_phase_end: phaseEndTime?.toISOString() || null
    };

    localStorage.setItem(`mock_game_${gameId}`, JSON.stringify(updatedGame));
    setGameState(updatedGame);

    toast({
      title: "Phase Changed",
      description: `Game phase changed to: ${newPhase}`,
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'waiting': return 'secondary';
      case 'mafia_reveal': return 'destructive';
      case 'day_discussion': return 'default';
      case 'day_voting': return 'outline';
      case 'night': return 'secondary';
      case 'final_discussion': return 'default';
      case 'ended': return 'destructive';
      default: return 'secondary';
    }
  };

  const alivePlayers = players.filter(p => p.is_alive);
  const deadPlayers = players.filter(p => !p.is_alive);
  const mafiaPlayers = players.filter(p => p.role === 'mafia');
  const civilianPlayers = players.filter(p => p.role === 'civilian');

  if (!gameId) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">No game ID provided for testing</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Game Phase Tester
          </div>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={refresh}>
              Refresh
            </Button>
            <Badge variant="outline">{gameId}</Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {gameState ? (
          <>
            {/* Current Phase Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <Badge variant={getPhaseColor(gameState.status)} className="mb-2">
                    {gameState.status.replace('_', ' ').toUpperCase()}
                  </Badge>
                  <div className="text-2xl font-bold">
                    {timeLeft > 0 ? formatTime(timeLeft) : 'No Timer'}
                  </div>
                  {timeLeft > 0 && (
                    <Progress value={(timeLeft / 300) * 100} className="mt-2" />
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-sm text-muted-foreground mb-1">Players</div>
                  <div className="text-2xl font-bold">{players.length}</div>
                  <div className="text-sm">
                    {alivePlayers.length} alive, {deadPlayers.length} dead
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-sm text-muted-foreground mb-1">Roles</div>
                  <div className="text-lg">
                    {mafiaPlayers.length} Mafia, {civilianPlayers.length} Civilian
                  </div>
                  {gameState.winner && (
                    <Badge variant="default" className="mt-1">
                      Winner: {gameState.winner}
                    </Badge>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Phase Control Buttons */}
            <div className="space-y-3">
              <h3 className="font-semibold">Force Phase Change:</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <Button size="sm" variant="outline" onClick={() => forcePhaseChange('mafia_reveal')}>
                  Mafia Reveal
                </Button>
                <Button size="sm" variant="outline" onClick={() => forcePhaseChange('day_discussion')}>
                  Discussion
                </Button>
                <Button size="sm" variant="outline" onClick={() => forcePhaseChange('day_voting')}>
                  Voting
                </Button>
                <Button size="sm" variant="outline" onClick={() => forcePhaseChange('night')}>
                  Night
                </Button>
                <Button size="sm" variant="outline" onClick={() => forcePhaseChange('final_discussion')}>
                  Final Discussion
                </Button>
                <Button size="sm" variant="outline" onClick={() => forcePhaseChange('ended')}>
                  End Game
                </Button>
              </div>
            </div>

            {/* Players List */}
            <div className="space-y-3">
              <h3 className="font-semibold">Players:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {players.map((player) => (
                  <div
                    key={player.id}
                    className="flex items-center justify-between p-3 rounded-lg bg-muted"
                  >
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{player.name}</span>
                      {player.user_id === user?.id && <Badge variant="secondary">You</Badge>}
                      {player.user_id === gameState.host_id && <Crown className="w-4 h-4 text-amber-500" />}
                      {!player.is_alive && <Skull className="w-4 h-4 text-red-500" />}
                    </div>
                    <div className="flex items-center gap-2">
                      {player.role && (
                        <Badge variant={player.role === 'mafia' ? 'destructive' : 'default'}>
                          {player.role}
                        </Badge>
                      )}
                      <Badge variant={player.is_ready ? 'default' : 'secondary'}>
                        {player.is_ready ? 'Ready' : 'Not Ready'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex gap-2 flex-wrap">
              <Button size="sm" onClick={() => window.location.href = `/waiting/${gameId}`}>
                Go to Waiting Room
              </Button>
              <Button size="sm" onClick={() => window.location.href = `/game/${gameId}`}>
                Go to Game Interface
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No game data found for ID: {gameId}</p>
            <Button className="mt-4" onClick={() => window.location.href = '/'}>
              Return to Home
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GamePhaseTester;
