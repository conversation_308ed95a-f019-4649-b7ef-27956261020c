import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Users, Zap, AlertCircle } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard = ({ children }: AuthGuardProps) => {
  const { user, loading, signIn, signUp, signInAnonymously } = useAuth();
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-muted to-background flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (user) {
    return <>{children}</>;
  }

  const handleSubmit = async (action: 'signin' | 'signup' | 'anonymous') => {
    setIsSubmitting(true);
    setError(null);

    try {
      if (action === 'signin') {
        await signIn(email, password);
        toast({
          title: "Welcome back!",
          description: "You have successfully signed in.",
        });
      } else if (action === 'signup') {
        await signUp(email, password, name);
        toast({
          title: "Account created!",
          description: "Welcome to the Mafia game.",
        });
      } else {
        await signInAnonymously(name);
        toast({
          title: "Welcome!",
          description: `Playing as ${name}. Let's start the game!`,
        });
      }
    } catch (error: any) {
      console.error('Auth error:', error);
      const errorMessage = error.message || 'Authentication failed. Please try again.';
      setError(errorMessage);
      toast({
        title: "Authentication Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // If user is authenticated, render the children (main app)
  if (user) {
    console.log('User authenticated, rendering main app:', user.user_metadata?.name || user.email);
    return <>{children}</>;
  }

  // Show authentication form
  console.log('No user found, showing authentication form');
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted to-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-primary">
            Join the Mafia Game
          </CardTitle>
          <p className="text-muted-foreground">
            Sign in or play as guest to start
          </p>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-3 rounded-lg bg-destructive/10 border border-destructive/20 flex items-center gap-2 text-destructive">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}
          <Tabs defaultValue="anonymous" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="anonymous" className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Quick Play
              </TabsTrigger>
              <TabsTrigger value="account" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Account
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="anonymous" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="guest-name">Your Name</Label>
                <Input
                  id="guest-name"
                  placeholder="Enter your name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
              </div>
              <Button
                onClick={() => handleSubmit('anonymous')}
                disabled={!name.trim() || isSubmitting}
                className="w-full"
                size="lg"
              >
                <Zap className="w-5 h-5 mr-2" />
                {isSubmitting ? 'Joining...' : 'Play as Guest'}
              </Button>

              <div className="mt-4 text-center">
                <p className="text-xs text-muted-foreground">
                  Having trouble? Check the browser console for errors or try refreshing.
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="account" className="space-y-4">
              <Tabs defaultValue="signin" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="signin">Sign In</TabsTrigger>
                  <TabsTrigger value="signup">Sign Up</TabsTrigger>
                </TabsList>
                
                <TabsContent value="signin" className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="signin-email">Email</Label>
                    <Input
                      id="signin-email"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signin-password">Password</Label>
                    <Input
                      id="signin-password"
                      type="password"
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  </div>
                  <Button 
                    onClick={() => handleSubmit('signin')}
                    disabled={!email || !password || isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting ? 'Signing In...' : 'Sign In'}
                  </Button>
                </TabsContent>
                
                <TabsContent value="signup" className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="signup-name">Name</Label>
                    <Input
                      id="signup-name"
                      placeholder="Enter your name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signup-email">Email</Label>
                    <Input
                      id="signup-email"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signup-password">Password</Label>
                    <Input
                      id="signup-password"
                      type="password"
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  </div>
                  <Button 
                    onClick={() => handleSubmit('signup')}
                    disabled={!email || !password || !name || isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting ? 'Creating Account...' : 'Create Account'}
                  </Button>
                </TabsContent>
              </Tabs>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthGuard;