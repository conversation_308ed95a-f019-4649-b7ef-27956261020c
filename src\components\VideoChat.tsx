import React, { useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Camera, CameraOff, Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Player } from '@/hooks/useGame';
import { useVideoChat, VideoStream } from '@/hooks/useVideoChat';

interface VideoChatProps {
  gameId: string;
  players: Player[];
  currentPlayer: Player | null;
  gameStatus: string;
}

interface VideoPlayerProps {
  stream: VideoStream;
  player: Player | null;
  isCurrentPlayer: boolean;
  gameStatus: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ 
  stream, 
  player, 
  isCurrentPlayer, 
  gameStatus 
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current && stream.stream) {
      videoRef.current.srcObject = stream.stream;
    }
  }, [stream.stream]);

  const shouldShowVideo = () => {
    if (!player) return false;
    
    // During mafia reveal, only mafia can see each other
    if (gameStatus === 'mafia_reveal') {
      return player.role === 'mafia' || isCurrentPlayer;
    }
    
    // Eliminated players can see but not be seen (except by other eliminated players)
    if (!player.is_alive && !isCurrentPlayer) {
      return false;
    }
    
    return player.is_camera_on;
  };

  const shouldShowAudio = () => {
    if (!player) return false;
    
    // During mafia reveal, only mafia can hear each other
    if (gameStatus === 'mafia_reveal') {
      return player.role === 'mafia' || isCurrentPlayer;
    }
    
    // Eliminated players can hear but not be heard
    if (!player.is_alive && !isCurrentPlayer) {
      return false;
    }
    
    return player.is_mic_on;
  };

  return (
    <Card className={cn(
      "relative overflow-hidden transition-all duration-300 fade-in video-player",
      isCurrentPlayer && "ring-2 ring-primary",
      !player?.is_alive && "opacity-60 border-destructive"
    )}>
      <CardContent className="p-0 aspect-video bg-muted relative">
        {shouldShowVideo() ? (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted={isCurrentPlayer} // Mute own video to prevent feedback
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-muted">
            <Avatar className="w-16 h-16">
              <AvatarFallback className="text-2xl">
                {player?.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </div>
        )}
        
        {/* Player info overlay */}
        <div className="video-overlay">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge
                variant={player?.is_alive ? "default" : "destructive"}
                className="text-xs"
              >
                {player?.name}
                {isCurrentPlayer && " (You)"}
              </Badge>
              {!player?.is_alive && (
                <Badge variant="outline" className="text-xs">
                  Eliminated
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-1">
              {shouldShowAudio() ? (
                <Volume2 className="w-4 h-4 text-green-500" />
              ) : (
                <VolumeX className="w-4 h-4 text-red-500" />
              )}
              {shouldShowVideo() ? (
                <Camera className="w-4 h-4 text-green-500" />
              ) : (
                <CameraOff className="w-4 h-4 text-red-500" />
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const VideoChat: React.FC<VideoChatProps> = ({ 
  gameId, 
  players, 
  currentPlayer, 
  gameStatus 
}) => {
  const { videoStreams, videoControls, isInitialized } = useVideoChat(
    gameId, 
    players, 
    currentPlayer
  );

  if (!isInitialized) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing video chat...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Video Grid */}
      <div className="video-grid fade-in">
        {videoStreams.map((stream) => {
          const player = players.find(p => p.id === stream.playerId);
          return (
            <VideoPlayer
              key={stream.playerId}
              stream={stream}
              player={player || null}
              isCurrentPlayer={stream.isLocal}
              gameStatus={gameStatus}
            />
          );
        })}
      </div>

      {/* Video Controls */}
      {currentPlayer && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-center gap-4">
              <Button
                variant={videoControls.isCameraOn ? "default" : "destructive"}
                size="lg"
                onClick={videoControls.toggleCamera}
                className="flex items-center gap-2"
              >
                {videoControls.isCameraOn ? (
                  <Camera className="w-5 h-5" />
                ) : (
                  <CameraOff className="w-5 h-5" />
                )}
                {videoControls.isCameraOn ? "Camera On" : "Camera Off"}
              </Button>
              
              <Button
                variant={videoControls.isMicOn ? "default" : "destructive"}
                size="lg"
                onClick={videoControls.toggleMic}
                className="flex items-center gap-2"
              >
                {videoControls.isMicOn ? (
                  <Mic className="w-5 h-5" />
                ) : (
                  <MicOff className="w-5 h-5" />
                )}
                {videoControls.isMicOn ? "Mic On" : "Mic Off"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default VideoChat;
