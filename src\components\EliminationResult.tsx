import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Skull, Crown, Eye, Heart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EliminationResultProps {
  gameId: string;
  onComplete?: () => void;
}

interface EliminationData {
  playerName: string;
  playerRole: string;
  voteCount: number;
  eliminationType: 'eliminate' | 'mafia_kill';
  timestamp: number;
}

const EliminationResult: React.FC<EliminationResultProps> = ({ gameId, onComplete }) => {
  const [elimination, setElimination] = useState<EliminationData | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!gameId) return;

    // Listen for elimination events
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key && e.key.startsWith(`elimination_result_${gameId}`)) {
        try {
          const eliminationData = e.newValue ? JSON.parse(e.newValue) : null;
          if (eliminationData) {
            setElimination(eliminationData);
            setIsVisible(true);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
              setIsVisible(false);
              setTimeout(() => {
                setElimination(null);
                onComplete?.();
              }, 500);
            }, 5000);
          }
        } catch (error) {
          console.error('Error parsing elimination result:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [gameId, onComplete]);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'mafia':
        return <Skull className="w-5 h-5 text-destructive" />;
      case 'detective':
        return <Eye className="w-5 h-5 text-blue-500" />;
      case 'doctor':
        return <Heart className="w-5 h-5 text-green-500" />;
      default:
        return <Crown className="w-5 h-5 text-muted-foreground" />;
    }
  };

  const getEliminationTitle = (type: string) => {
    switch (type) {
      case 'eliminate':
        return 'Player Eliminated by Vote';
      case 'mafia_kill':
        return 'Player Killed by Mafia';
      default:
        return 'Player Eliminated';
    }
  };

  const getEliminationDescription = (type: string, voteCount: number) => {
    switch (type) {
      case 'eliminate':
        return `Eliminated with ${voteCount} vote${voteCount !== 1 ? 's' : ''}`;
      case 'mafia_kill':
        return 'Killed during the night phase';
      default:
        return 'Eliminated from the game';
    }
  };

  if (!elimination || !isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className={cn(
        "w-full max-w-md mx-auto border-2 animate-in fade-in-0 zoom-in-95 duration-500",
        elimination.eliminationType === 'eliminate' ? "border-orange-500" : "border-red-500"
      )}>
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2 text-xl">
            <Skull className="w-6 h-6 text-destructive" />
            {getEliminationTitle(elimination.eliminationType)}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Eliminated Player Display */}
          <div className="flex flex-col items-center space-y-4">
            <Avatar className="w-20 h-20 border-4 border-destructive">
              <AvatarFallback className="text-2xl bg-destructive/10">
                {elimination.playerName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div className="text-center space-y-2">
              <h3 className="text-2xl font-bold">{elimination.playerName}</h3>
              
              <div className="flex items-center justify-center gap-2">
                {getRoleIcon(elimination.playerRole)}
                <Badge 
                  variant={elimination.playerRole === 'mafia' ? 'destructive' : 'default'}
                  className="text-lg px-3 py-1"
                >
                  {elimination.playerRole.toUpperCase()}
                </Badge>
              </div>
              
              <p className="text-muted-foreground">
                {getEliminationDescription(elimination.eliminationType, elimination.voteCount)}
              </p>
            </div>
          </div>

          {/* Elimination Details */}
          <div className={cn(
            "p-4 rounded-lg border text-center",
            elimination.eliminationType === 'eliminate' 
              ? "bg-orange-50 border-orange-200" 
              : "bg-red-50 border-red-200"
          )}>
            <p className="text-sm font-medium">
              {elimination.eliminationType === 'eliminate' 
                ? '🗳️ Voted out by the town' 
                : '🌙 Eliminated by the mafia'
              }
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Their role has been revealed to all players
            </p>
          </div>

          {/* Auto-close indicator */}
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              This message will close automatically...
            </p>
            <div className="w-full h-1 bg-muted rounded-full mt-2 overflow-hidden">
              <div 
                className="h-full bg-primary rounded-full animate-pulse"
                style={{
                  animation: 'shrink 5s linear forwards'
                }}
              />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

export default EliminationResult;
