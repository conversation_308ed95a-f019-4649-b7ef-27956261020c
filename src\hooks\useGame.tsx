import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from '@/components/ui/use-toast';

export interface Game {
  id: string;
  host_id: string;
  status: 'waiting' | 'mafia_reveal' | 'day_discussion' | 'day_voting' | 'night' | 'final_discussion' | 'ended';
  max_players: number;
  mafia_count: number;
  discussion_time: number;
  is_public: boolean;
  game_name?: string;
  current_phase_end?: string;
  winner?: string;
  created_at: string;
  updated_at: string;
}

export interface Player {
  id: string;
  game_id: string;
  user_id?: string;
  name: string;
  role?: 'mafia' | 'civilian' | 'detective' | 'doctor';
  is_alive: boolean;
  is_ready: boolean;
  is_camera_on: boolean;
  is_mic_on: boolean;
  joined_at: string;
}

export interface Vote {
  id: string;
  game_id: string;
  voter_id: string;
  target_id?: string;
  vote_type: 'eliminate' | 'mafia_kill';
  created_at: string;
}

export interface GameMessage {
  id: string;
  game_id: string;
  player_id: string;
  message: string;
  is_mafia_chat: boolean;
  created_at: string;
  player?: Player;
}

export const useGame = (gameId: string) => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Helper function to trigger storage events for real-time updates
  const triggerStorageUpdate = (key: string, value: string) => {
    window.dispatchEvent(new StorageEvent('storage', {
      key,
      newValue: value,
      storageArea: localStorage
    }));
  };
  const [game, setGame] = useState<Game | null>(null);
  const [players, setPlayers] = useState<Player[]>([]);
  const [votes, setVotes] = useState<Vote[]>([]);
  const [messages, setMessages] = useState<GameMessage[]>([]);
  const [currentPlayer, setCurrentPlayer] = useState<Player | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(Date.now());

  // Fetch initial game data
  useEffect(() => {
    if (!gameId) return;

    const fetchGameData = async () => {
      try {
        // Check if this is a mock game first
        console.log('Checking for mock game with ID:', gameId);
        const mockGameData = localStorage.getItem(`mock_game_${gameId}`);
        console.log('Mock game data found:', mockGameData !== null);

        if (mockGameData) {
          console.log('Loading mock game data:', gameId);
          const parsedGame = JSON.parse(mockGameData);
          console.log('Parsed mock game:', parsedGame);
          setGame(parsedGame);

          // Load mock players
          const mockPlayers: Player[] = [];
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(`mock_player_${gameId}_`)) {
              const playerData = localStorage.getItem(key);
              if (playerData) {
                mockPlayers.push(JSON.parse(playerData));
              }
            }
          }
          setPlayers(mockPlayers);

          // Load mock votes
          const mockVotes: Vote[] = [];
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(`mock_vote_${gameId}_`)) {
              try {
                const voteData = localStorage.getItem(key);
                if (voteData) {
                  mockVotes.push(JSON.parse(voteData));
                }
              } catch (error) {
                console.error('Error parsing vote:', error);
              }
            }
          }
          setVotes(mockVotes);

          // Load mock messages
          const mockMessages: any[] = [];
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(`mock_message_${gameId}_`)) {
              try {
                const messageData = localStorage.getItem(key);
                if (messageData) {
                  mockMessages.push(JSON.parse(messageData));
                }
              } catch (error) {
                console.error('Error parsing message:', error);
              }
            }
          }
          setMessages(mockMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()));

          // Find current player
          if (user) {
            const currentPlayerData = mockPlayers.find(p => p.user_id === user.id);
            setCurrentPlayer(currentPlayerData || null);
          }

          setLoading(false);
          return;
        }

        // Fetch game from database
        const { data: gameData, error: gameError } = await supabase
          .from('games')
          .select('*')
          .eq('id', gameId)
          .single();

        if (gameError) throw gameError;
        setGame(gameData);

        // Fetch players
        const { data: playersData, error: playersError } = await supabase
          .from('players')
          .select('*')
          .eq('game_id', gameId)
          .order('joined_at');

        if (playersError) throw playersError;
        setPlayers(playersData);

        // Find current player
        if (user) {
          const currentPlayerData = playersData.find(p => p.user_id === user.id);
          setCurrentPlayer(currentPlayerData || null);
        }

        // Fetch votes
        const { data: votesData, error: votesError } = await supabase
          .from('votes')
          .select('*')
          .eq('game_id', gameId);

        if (votesError) throw votesError;
        setVotes(votesData as Vote[]);

        // Fetch messages
        const { data: messagesData, error: messagesError } = await supabase
          .from('game_messages')
          .select(`
            *,
            player:players(*)
          `)
          .eq('game_id', gameId)
          .order('created_at');

        if (messagesError) throw messagesError;
        setMessages(messagesData);

      } catch (error) {
        console.error('Error fetching game data:', error);
        toast({
          title: "Error",
          description: "Failed to load game data",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchGameData();
  }, [gameId, user, toast]);

  // Real-time updates for mock games
  useEffect(() => {
    if (!gameId) return;

    const isMockGame = localStorage.getItem(`mock_game_${gameId}`) !== null;
    if (!isMockGame) return;

    console.log('Setting up real-time updates for mock game:', gameId);

    // Function to reload mock game data
    const reloadMockGameData = () => {
      console.log('Reloading mock game data...');

      // Reload game data
      const gameData = localStorage.getItem(`mock_game_${gameId}`);
      if (gameData) {
        const parsedGame = JSON.parse(gameData);
        setGame(parsedGame);
      }

      // Reload players
      const mockPlayers: Player[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(`mock_player_${gameId}_`)) {
          try {
            const playerData = localStorage.getItem(key);
            if (playerData) {
              mockPlayers.push(JSON.parse(playerData));
            }
          } catch (error) {
            console.error('Error parsing player:', error);
          }
        }
      }
      setPlayers(mockPlayers);

      // Update current player
      if (user) {
        const updatedCurrentPlayer = mockPlayers.find(p => p.user_id === user.id);
        setCurrentPlayer(updatedCurrentPlayer || null);
      }

      setLastUpdate(Date.now());
    };

    // Listen for localStorage changes (cross-tab communication)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key && (
        e.key.startsWith(`mock_game_${gameId}`) ||
        e.key.startsWith(`mock_player_${gameId}_`) ||
        e.key.startsWith(`mock_vote_${gameId}_`) ||
        e.key.startsWith(`mock_message_${gameId}_`)
      )) {
        console.log('Storage change detected for mock game:', e.key);
        reloadMockGameData();
      }
    };

    // Set up polling for real-time updates (fallback for same-tab updates)
    const pollInterval = setInterval(() => {
      reloadMockGameData();
    }, 2000); // Poll every 2 seconds

    // Listen for storage events (cross-tab updates)
    window.addEventListener('storage', handleStorageChange);

    // Cleanup
    return () => {
      clearInterval(pollInterval);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [gameId, user]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!gameId) return;

    const gameChannel = supabase
      .channel(`game-${gameId}`)
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'games', filter: `id=eq.${gameId}` },
        (payload) => {
          setGame(payload.new as Game);
        }
      )
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'players', filter: `game_id=eq.${gameId}` },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setPlayers(prev => [...prev, payload.new as Player].sort((a, b) => 
              new Date(a.joined_at).getTime() - new Date(b.joined_at).getTime()
            ));
          } else if (payload.eventType === 'UPDATE') {
            setPlayers(prev => prev.map(p => 
              p.id === payload.new.id ? payload.new as Player : p
            ));
          } else if (payload.eventType === 'DELETE') {
            setPlayers(prev => prev.filter(p => p.id !== payload.old.id));
          }
        }
      )
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'votes', filter: `game_id=eq.${gameId}` },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setVotes(prev => [...prev, payload.new as Vote]);
          } else if (payload.eventType === 'UPDATE') {
            setVotes(prev => prev.map(v => 
              v.id === payload.new.id ? payload.new as Vote : v
            ));
          } else if (payload.eventType === 'DELETE') {
            setVotes(prev => prev.filter(v => v.id !== payload.old.id));
          }
        }
      )
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'game_messages', filter: `game_id=eq.${gameId}` },
        async (payload) => {
          const { data: playerData } = await supabase
            .from('players')
            .select('*')
            .eq('id', payload.new.player_id)
            .single();

          setMessages(prev => [...prev, { 
            ...payload.new as GameMessage, 
            player: playerData 
          }]);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(gameChannel);
    };
  }, [gameId]);

  const joinGame = async (name: string) => {
    if (!user || !game) return;

    try {
      // Check if this is a mock game
      const mockGameData = localStorage.getItem(`mock_game_${gameId}`);
      if (mockGameData) {
        console.log('Joining mock game:', gameId);

        // Create mock player
        const mockPlayer = {
          id: `player_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          game_id: gameId,
          user_id: user.id,
          name: name,
          role: null,
          is_alive: true,
          is_ready: false,
          is_camera_on: true,
          is_mic_on: true,
          joined_at: new Date().toISOString()
        };

        localStorage.setItem(`mock_player_${gameId}_${user.id}`, JSON.stringify(mockPlayer));

        // Trigger storage event for cross-tab updates
        triggerStorageUpdate(`mock_player_${gameId}_${user.id}`, JSON.stringify(mockPlayer));

        // Update players list
        setPlayers(prev => [...prev, mockPlayer]);
        setCurrentPlayer(mockPlayer);

        toast({
          title: "Joined Mock Game",
          description: "You have successfully joined the mock game",
        });
        return;
      }

      // Regular database join
      const { error } = await supabase
        .from('players')
        .insert({
          game_id: gameId,
          user_id: user.id,
          name: name,
        });

      if (error) throw error;

      toast({
        title: "Joined Game",
        description: "You have successfully joined the game",
      });
    } catch (error) {
      console.error('Error joining game:', error);
      toast({
        title: "Error",
        description: "Failed to join game",
        variant: "destructive",
      });
    }
  };

  const toggleReady = async () => {
    if (!currentPlayer) return;

    try {
      // Check if this is a mock game
      const mockGameData = localStorage.getItem(`mock_game_${gameId}`);
      if (mockGameData) {
        console.log('Toggling ready status in mock game');

        // Update mock player
        const updatedPlayer = { ...currentPlayer, is_ready: !currentPlayer.is_ready };
        localStorage.setItem(`mock_player_${gameId}_${currentPlayer.user_id}`, JSON.stringify(updatedPlayer));

        // Trigger storage event for cross-tab updates
        triggerStorageUpdate(`mock_player_${gameId}_${currentPlayer.user_id}`, JSON.stringify(updatedPlayer));

        // Update state
        setCurrentPlayer(updatedPlayer);
        setPlayers(prev => prev.map(p => p.id === currentPlayer.id ? updatedPlayer : p));
        return;
      }

      // Regular database update
      const { error } = await supabase
        .from('players')
        .update({ is_ready: !currentPlayer.is_ready })
        .eq('id', currentPlayer.id);

      if (error) throw error;
    } catch (error) {
      console.error('Error toggling ready:', error);
      toast({
        title: "Error",
        description: "Failed to update ready status",
        variant: "destructive",
      });
    }
  };

  const startGame = async () => {
    if (!game || !user) return;

    try {
      // Check if this is a mock game
      const mockGameData = localStorage.getItem(`mock_game_${gameId}`);
      if (mockGameData) {
        console.log('Starting mock game');

        // Assign roles to mock players
        const mockPlayers = [...players];
        const mafiaCount = game.mafia_count || 1;

        // Shuffle players and assign roles
        const shuffled = [...mockPlayers].sort(() => Math.random() - 0.5);
        for (let i = 0; i < shuffled.length; i++) {
          const role = i < mafiaCount ? 'mafia' : 'civilian';
          shuffled[i].role = role as any;

          // Update localStorage
          localStorage.setItem(`mock_player_${gameId}_${shuffled[i].user_id}`, JSON.stringify(shuffled[i]));
        }

        // Update game status with phase timing
        const now = new Date();
        const phaseEndTime = new Date(now.getTime() + 5000); // 5 seconds for mafia reveal

        const updatedGame = {
          ...game,
          status: 'mafia_reveal' as any,
          current_phase_end: phaseEndTime.toISOString()
        };
        localStorage.setItem(`mock_game_${gameId}`, JSON.stringify(updatedGame));

        // Trigger storage event for cross-tab updates
        triggerStorageUpdate(`mock_game_${gameId}`, JSON.stringify(updatedGame));

        // Update state
        setPlayers(shuffled);
        setGame(updatedGame);

        // Update current player
        const updatedCurrentPlayer = shuffled.find(p => p.user_id === user.id);
        if (updatedCurrentPlayer) {
          setCurrentPlayer(updatedCurrentPlayer);
        }

        // Set up automatic phase progression for mock games
        setTimeout(() => {
          progressToNextPhase('day_discussion');
        }, 5000);

        toast({
          title: "Mock Game Started!",
          description: "Roles have been assigned. Good luck!",
        });
        return;
      }

      // Regular database start (only if host)
      if (game.host_id !== user.id) return;

      // Assign roles
      await supabase.functions.invoke('assign-roles', {
        body: { gameId }
      });

      toast({
        title: "Game Started",
        description: "Roles have been assigned. The game begins!",
      });
    } catch (error) {
      console.error('Error starting game:', error);
      toast({
        title: "Error",
        description: "Failed to start game",
        variant: "destructive",
      });
    }
  };

  // Function to tally votes and eliminate player
  const tallyVotesAndEliminate = (voteType: 'eliminate' | 'mafia_kill') => {
    if (!gameId) return;

    console.log(`Tallying votes for ${voteType}...`);

    // Get all votes for this game and vote type
    const allVotes: any[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(`mock_vote_${gameId}_`)) {
        try {
          const voteData = localStorage.getItem(key);
          if (voteData) {
            const vote = JSON.parse(voteData);
            if (vote.vote_type === voteType) {
              allVotes.push(vote);
            }
          }
        } catch (error) {
          console.error('Error parsing vote:', error);
        }
      }
    }

    if (allVotes.length === 0) {
      console.log('No votes found, no elimination');
      return;
    }

    // Count votes by target
    const voteCounts: { [targetId: string]: number } = {};
    allVotes.forEach(vote => {
      voteCounts[vote.target_id] = (voteCounts[vote.target_id] || 0) + 1;
    });

    // Find player with most votes
    let maxVotes = 0;
    let eliminatedPlayerId = '';
    Object.entries(voteCounts).forEach(([targetId, count]) => {
      if (count > maxVotes) {
        maxVotes = count;
        eliminatedPlayerId = targetId;
      }
    });

    if (!eliminatedPlayerId) {
      console.log('No clear elimination target');
      return;
    }

    // Find the eliminated player from the players array to get their user_id
    const eliminatedPlayer = players.find(p => p.id === eliminatedPlayerId);
    if (!eliminatedPlayer) {
      console.log('Eliminated player not found in players array');
      return;
    }

    // Get the eliminated player data from localStorage using user_id
    const eliminatedPlayerData = localStorage.getItem(`mock_player_${gameId}_${eliminatedPlayer.user_id}`);
    if (!eliminatedPlayerData) {
      console.log('Eliminated player data not found in localStorage');
      return;
    }

    const eliminatedPlayerFromStorage = JSON.parse(eliminatedPlayerData);

    // Update player to eliminated
    const updatedPlayer = { ...eliminatedPlayerFromStorage, is_alive: false };
    localStorage.setItem(`mock_player_${gameId}_${eliminatedPlayer.user_id}`, JSON.stringify(updatedPlayer));

    // Trigger storage event for real-time updates
    triggerStorageUpdate(`mock_player_${gameId}_${eliminatedPlayer.user_id}`, JSON.stringify(updatedPlayer));

    // Clear all votes for this vote type
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(`mock_vote_${gameId}_`)) {
        try {
          const voteData = localStorage.getItem(key);
          if (voteData) {
            const vote = JSON.parse(voteData);
            if (vote.vote_type === voteType) {
              localStorage.removeItem(key);
            }
          }
        } catch (error) {
          console.error('Error clearing vote:', error);
        }
      }
    }

    // Store elimination result for display
    const eliminationResult = {
      playerName: eliminatedPlayer.name,
      playerRole: eliminatedPlayer.role,
      voteCount: maxVotes,
      eliminationType: voteType,
      timestamp: Date.now()
    };

    localStorage.setItem(`elimination_result_${gameId}`, JSON.stringify(eliminationResult));

    // Trigger storage event for elimination result display
    triggerStorageUpdate(`elimination_result_${gameId}`, JSON.stringify(eliminationResult));

    // Show elimination result toast as backup
    toast({
      title: `${voteType === 'eliminate' ? 'Player Eliminated' : 'Player Killed'}`,
      description: `${eliminatedPlayer.name} (${eliminatedPlayer.role?.toUpperCase()}) has been eliminated with ${maxVotes} vote(s)`,
      duration: 5000,
    });

    console.log(`Eliminated ${eliminatedPlayer.name} (${eliminatedPlayer.role}) with ${maxVotes} votes`);
  };

  // Function to progress mock game phases
  const progressToNextPhase = (nextPhase: string) => {
    const mockGameData = localStorage.getItem(`mock_game_${gameId}`);
    if (!mockGameData || !game) return;

    console.log(`Progressing mock game to phase: ${nextPhase}`);

    const now = new Date();
    let phaseDuration = 0;

    // Set phase duration based on phase type
    switch (nextPhase) {
      case 'day_discussion':
        phaseDuration = (game.discussion_time || 120) * 1000; // Convert to milliseconds
        break;
      case 'day_voting':
        phaseDuration = 60 * 1000; // 60 seconds for voting
        break;
      case 'night':
        phaseDuration = 30 * 1000; // 30 seconds for night phase
        break;
      case 'final_discussion':
        phaseDuration = 60 * 1000; // 60 seconds for final discussion
        break;
      case 'ended':
        phaseDuration = 0; // No timer for ended phase
        break;
      default:
        phaseDuration = 30 * 1000;
    }

    const phaseEndTime = phaseDuration > 0 ? new Date(now.getTime() + phaseDuration) : null;

    const updatedGame = {
      ...game,
      status: nextPhase,
      current_phase_end: phaseEndTime?.toISOString() || null
    };

    localStorage.setItem(`mock_game_${gameId}`, JSON.stringify(updatedGame));
    setGame(updatedGame);

    // Set up next phase progression (except for ended phase)
    if (nextPhase !== 'ended' && phaseDuration > 0) {
      setTimeout(() => {
        const nextPhaseMap: { [key: string]: string } = {
          'day_discussion': 'day_voting',
          'day_voting': 'night',
          'night': 'day_discussion', // This would check win conditions first
          'final_discussion': 'ended'
        };

        const following = nextPhaseMap[nextPhase];
        if (following) {
          // Handle vote tallying for voting phases
          if (nextPhase === 'day_voting') {
            tallyVotesAndEliminate('eliminate');
            // Delay progression to show elimination results
            setTimeout(() => {
              checkWinConditions() || progressToNextPhase(following);
            }, 3000);
          } else if (nextPhase === 'night') {
            tallyVotesAndEliminate('mafia_kill');
            // Delay progression to show elimination results
            setTimeout(() => {
              checkWinConditions() || progressToNextPhase(following);
            }, 3000);
          } else {
            // Check win conditions before progressing
            if (nextPhase === 'day_voting' || nextPhase === 'night') {
              checkWinConditions() || progressToNextPhase(following);
            } else {
              progressToNextPhase(following);
            }
          }
        }
      }, phaseDuration);
    }
  };

  // Function to check win conditions for mock games
  const checkWinConditions = (): boolean => {
    if (!game || !players.length) return false;

    const alivePlayers = players.filter(p => p.is_alive);
    const aliveMafia = alivePlayers.filter(p => p.role === 'mafia');
    const aliveCivilians = alivePlayers.filter(p => p.role === 'civilian');

    let winner = null;

    if (aliveMafia.length === 0) {
      winner = 'civilian';
    } else if (aliveMafia.length >= aliveCivilians.length) {
      winner = 'mafia';
    }

    if (winner) {
      console.log(`Game ended! Winner: ${winner}`);
      const updatedGame = {
        ...game,
        status: 'final_discussion',
        winner: winner,
        current_phase_end: new Date(Date.now() + 60000).toISOString() // 1 minute final discussion
      };

      localStorage.setItem(`mock_game_${gameId}`, JSON.stringify(updatedGame));
      setGame(updatedGame);

      // End game after final discussion
      setTimeout(() => {
        const endedGame = { ...updatedGame, status: 'ended', current_phase_end: null };
        localStorage.setItem(`mock_game_${gameId}`, JSON.stringify(endedGame));
        setGame(endedGame);
      }, 60000);

      return true;
    }

    return false;
  };

  const vote = async (targetId: string, voteType: 'eliminate' | 'mafia_kill') => {
    if (!currentPlayer) return;

    try {
      // Check if this is a mock game
      const mockGameData = localStorage.getItem(`mock_game_${gameId}`);
      if (mockGameData) {
        console.log(`Mock vote: ${currentPlayer.name} votes for ${targetId} (${voteType})`);

        // Create mock vote
        const mockVote = {
          id: `vote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          game_id: gameId,
          voter_id: currentPlayer.id,
          target_id: targetId,
          vote_type: voteType,
          created_at: new Date().toISOString()
        };

        // Store vote in localStorage
        const voteKey = `mock_vote_${gameId}_${currentPlayer.id}_${voteType}`;
        localStorage.setItem(voteKey, JSON.stringify(mockVote));

        // Update votes state
        setVotes(prev => {
          const filtered = prev.filter(v => !(v.voter_id === currentPlayer.id && v.vote_type === voteType));
          return [...filtered, mockVote];
        });

        toast({
          title: "Vote Submitted",
          description: `You voted for ${players.find(p => p.id === targetId)?.name}`,
        });

        return;
      }

      // Regular database vote
      const { error } = await supabase
        .from('votes')
        .upsert({
          game_id: gameId,
          voter_id: currentPlayer.id,
          target_id: targetId,
          vote_type: voteType,
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error voting:', error);
      toast({
        title: "Error",
        description: "Failed to submit vote",
        variant: "destructive",
      });
    }
  };

  const sendMessage = async (message: string, isMafiaChat = false) => {
    if (!currentPlayer) return;

    try {
      // Check if this is a mock game
      const mockGameData = localStorage.getItem(`mock_game_${gameId}`);
      if (mockGameData) {
        console.log(`Mock message: ${currentPlayer.name} says "${message}" (mafia: ${isMafiaChat})`);

        // Create mock message
        const mockMessage = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          game_id: gameId,
          player_id: currentPlayer.id,
          message,
          is_mafia_chat: isMafiaChat,
          created_at: new Date().toISOString(),
          player: currentPlayer
        };

        // Store message in localStorage
        const messageKey = `mock_message_${gameId}_${Date.now()}`;
        localStorage.setItem(messageKey, JSON.stringify(mockMessage));

        // Update messages state
        setMessages(prev => [...prev, mockMessage]);

        return;
      }

      // Regular database message
      const { error } = await supabase
        .from('game_messages')
        .insert({
          game_id: gameId,
          player_id: currentPlayer.id,
          message,
          is_mafia_chat: isMafiaChat,
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    }
  };

  return {
    game,
    players,
    votes,
    messages,
    currentPlayer,
    loading,
    joinGame,
    toggleReady,
    startGame,
    vote,
    sendMessage,
  };
};