-- Fix foreign key constraints to allow guest users
-- Remove the foreign key constraint on host_id to allow guest users
ALTER TABLE public.games DROP CONSTRAINT IF EXISTS games_host_id_fkey;

-- Remove the foreign key constraint on user_id in players table to allow guest users  
ALTER TABLE public.players DROP CONSTRAINT IF EXISTS players_user_id_fkey;

-- Update RLS policies to work with guest users
DROP POLICY IF EXISTS "Game hosts can update their games" ON public.games;
CREATE POLICY "Game hosts can update their games" ON public.games FOR UPDATE USING (
  host_id = auth.uid() OR host_id::text LIKE 'guest_%'
);

DROP POLICY IF EXISTS "Players can update themselves" ON public.players;
CREATE POLICY "Players can update themselves" ON public.players FOR UPDATE USING (
  user_id = auth.uid() OR user_id::text LIKE 'guest_%'
);

DROP POLICY IF EXISTS "Game hosts can manage players" ON public.players;
CREATE POLICY "Game hosts can manage players" ON public.players FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.games 
    WHERE games.id = players.game_id 
    AND (games.host_id = auth.uid() OR games.host_id::text LIKE 'guest_%')
  )
);

-- Update vote policies to work with guest users
DROP POLICY IF EXISTS "Players can view votes in their game" ON public.votes;
CREATE POLICY "Players can view votes in their game" ON public.votes FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.players 
    WHERE players.id = votes.voter_id 
    AND (players.user_id = auth.uid() OR players.user_id::text LIKE 'guest_%')
  )
  OR EXISTS (
    SELECT 1 FROM public.players 
    WHERE players.game_id = votes.game_id 
    AND (players.user_id = auth.uid() OR players.user_id::text LIKE 'guest_%')
  )
);

DROP POLICY IF EXISTS "Players can vote" ON public.votes;
CREATE POLICY "Players can vote" ON public.votes FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.players 
    WHERE players.id = votes.voter_id 
    AND (players.user_id = auth.uid() OR players.user_id::text LIKE 'guest_%')
  )
);

DROP POLICY IF EXISTS "Players can update their votes" ON public.votes;
CREATE POLICY "Players can update their votes" ON public.votes FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM public.players 
    WHERE players.id = votes.voter_id 
    AND (players.user_id = auth.uid() OR players.user_id::text LIKE 'guest_%')
  )
);

-- Update message policies to work with guest users
DROP POLICY IF EXISTS "Players can view messages in their game" ON public.game_messages;
CREATE POLICY "Players can view messages in their game" ON public.game_messages FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.players 
    WHERE players.game_id = game_messages.game_id 
    AND (players.user_id = auth.uid() OR players.user_id::text LIKE 'guest_%')
  )
  AND (
    is_mafia_chat = false 
    OR EXISTS (
      SELECT 1 FROM public.players 
      WHERE players.game_id = game_messages.game_id 
      AND (players.user_id = auth.uid() OR players.user_id::text LIKE 'guest_%')
      AND players.role = 'mafia'
    )
  )
);

DROP POLICY IF EXISTS "Players can send messages" ON public.game_messages;
CREATE POLICY "Players can send messages" ON public.game_messages FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.players 
    WHERE players.id = game_messages.player_id 
    AND (players.user_id = auth.uid() OR players.user_id::text LIKE 'guest_%')
  )
);
