import { useParams, Navigate } from 'react-router-dom';
import { useGame } from '@/hooks/useGame';
import { useAuth } from '@/hooks/useAuth';
import GameInterface from '@/components/GameInterface';
import GamePhaseTester from '@/components/GamePhaseTester';

const GamePage = () => {
  const { gameId } = useParams<{ gameId: string }>();
  const { user } = useAuth();
  const { game, loading } = useGame(gameId || '');

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-muted to-background flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!game) {
    return <Navigate to="/" replace />;
  }

  if (game.status === 'waiting') {
    return <Navigate to={`/waiting/${gameId}`} replace />;
  }

  return (
    <div className="space-y-6">
      {/* Show Game Phase Tester for mock games */}
      {gameId && localStorage.getItem(`mock_game_${gameId}`) && (
        <GamePhaseTester gameId={gameId} />
      )}
      <GameInterface gameId={gameId || ''} />
    </div>
  );
};

export default GamePage;