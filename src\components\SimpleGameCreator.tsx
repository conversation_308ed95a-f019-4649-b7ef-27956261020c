import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';

const SimpleGameCreator: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [gameName, setGameName] = useState('Test Game');
  const [isCreating, setIsCreating] = useState(false);

  const createSimpleGame = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "No user found. Please authenticate first.",
        variant: "destructive"
      });
      return;
    }

    setIsCreating(true);

    try {
      // Generate a simple game ID
      const gameId = `GAME_${Date.now().toString().slice(-6)}`;
      
      console.log('Creating simple game:', {
        gameId,
        hostId: user.id,
        gameName,
        userType: user.id.startsWith('guest_') ? 'guest' : 'authenticated'
      });

      // Try to create a temporary auth user for guest users first
      let actualHostId = user.id;

      if (user.id.startsWith('guest_')) {
        console.log('Guest user detected, using alternative approach');
        // For guest users, we'll use a special host_id that bypasses foreign key constraints
        actualHostId = user.id;
      }

      // Create game with minimal data
      const gameInsertData = {
        id: gameId,
        host_id: actualHostId,
        game_name: gameName,
        max_players: 3,
        mafia_count: 1,
        discussion_time: 60,
        is_public: false,
        status: 'waiting' as const
      };

      console.log('Inserting game data:', gameInsertData);

      const { data: gameData, error: gameError } = await supabase
        .from('games')
        .insert(gameInsertData)
        .select()
        .single();

      if (gameError) {
        console.error('Game creation error:', gameError);
        throw gameError;
      }

      console.log('Game created successfully:', gameData);

      // Add host as player
      const hostName = user.user_metadata?.name || 'Host';
      const { data: playerData, error: playerError } = await supabase
        .from('players')
        .insert({
          game_id: gameId,
          user_id: user.id,
          name: hostName,
          is_ready: false
        })
        .select()
        .single();

      if (playerError) {
        console.error('Player creation error:', playerError);
        // Don't fail the whole process, just log it
        toast({
          title: "Warning",
          description: "Game created but failed to add host as player",
          variant: "destructive"
        });
      } else {
        console.log('Host added as player:', playerData);
      }

      toast({
        title: "Success!",
        description: `Game created with ID: ${gameId}`,
      });

      // Navigate to waiting room
      navigate(`/waiting/${gameId}`);

    } catch (error: any) {
      console.error('Error in createSimpleGame:', error);

      // Log detailed error information
      console.error('Detailed error info:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        user: user,
        userType: user?.id?.startsWith('guest_') ? 'guest' : 'authenticated'
      });

      toast({
        title: "Game Creation Failed",
        description: `${error.message || 'Unknown error'}. Check console for details.`,
        variant: "destructive"
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Simple Game Creator</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="game-name">Game Name</Label>
          <Input
            id="game-name"
            value={gameName}
            onChange={(e) => setGameName(e.target.value)}
            placeholder="Enter game name"
          />
        </div>

        <div className="p-3 bg-muted rounded-lg text-sm">
          <p><strong>Settings:</strong></p>
          <p>• 3 players maximum</p>
          <p>• 1 mafia member</p>
          <p>• 60 seconds discussion time</p>
          <p>• Private game</p>
        </div>

        <Button 
          onClick={createSimpleGame}
          disabled={!gameName.trim() || isCreating}
          className="w-full"
        >
          {isCreating ? 'Creating...' : 'Create Simple Game'}
        </Button>

        {user && (
          <div className="p-2 bg-blue-50 rounded text-xs">
            <p><strong>User:</strong> {user.user_metadata?.name || user.email || 'Guest'}</p>
            <p><strong>ID:</strong> {user.id}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SimpleGameCreator;
