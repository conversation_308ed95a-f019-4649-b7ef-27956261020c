import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Users, Video, Crown, Eye, Globe } from "lucide-react";
import { useNavigate } from "react-router-dom";
import PublicGameBrowser from "./PublicGameBrowser";
import DatabaseTest from "./DatabaseTest";
import SimpleGameCreator from "./SimpleGameCreator";
import MockGameCreator from "./MockGameCreator";
import DatabaseFixer from "./DatabaseFixer";
import MultiPlayerTester from "./MultiPlayerTester";
import MultiTabTester from "./MultiTabTester";
import MockGameDebugger from "./MockGameDebugger";
import UserManager from "./UserManager";
import BrowserTester from "./BrowserTester";
import SimpleMultiTabTester from "./SimpleMultiTabTester";

const GameLanding = () => {
  const navigate = useNavigate();
  const [showJoinForm, setShowJoinForm] = useState(false);
  const [showPublicGames, setShowPublicGames] = useState(false);
  const [gameId, setGameId] = useState("");

  const features = [
    {
      icon: <Video className="w-8 h-8" />,
      title: "Real-Time Video Chat",
      description: "See and hear other players with WebRTC technology"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Role-Based Gameplay",
      description: "Experience authentic Mafia dynamics with hidden roles"
    },
    {
      icon: <Crown className="w-8 h-8" />,
      title: "Host Controls",
      description: "Customize game settings and manage players"
    },
    {
      icon: <Eye className="w-8 h-8" />,
      title: "Observer Mode",
      description: "Eliminated players can watch the game unfold"
    }
  ];

  const handleJoinGame = () => {
    if (gameId.trim()) {
      console.log('Joining game with ID:', gameId);
      // Navigate to waiting room, not directly to game
      navigate(`/waiting/${gameId}`);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="max-w-6xl w-full space-y-12">
        {/* Hero Section */}
        <div className="text-center space-y-6">
          <div className="space-y-4">
            <h1 className="text-6xl md:text-8xl font-bold text-glow">
              <span className="text-primary">MAFIA</span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-2xl mx-auto">
              The ultimate social deduction game with real-time video chat. 
              Discover who among you is the mafia before it's too late.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              size="lg" 
              variant="mafia"
              className="text-lg px-8 py-6 h-auto"
              onClick={() => navigate("/create")}
            >
              <Crown className="w-5 h-5" />
              Create Game
            </Button>
            
            {!showJoinForm ? (
              <Button 
                size="lg" 
                variant="ghost-glow"
                className="text-lg px-8 py-6 h-auto"
                onClick={() => setShowJoinForm(true)}
              >
                <Users className="w-5 h-5" />
                Join Game
              </Button>
            ) : (
              <div className="flex gap-2 items-center">
                <input
                  type="text"
                  placeholder="Enter Game ID (e.g., MOCK_123456)"
                  value={gameId}
                  onChange={(e) => setGameId(e.target.value)}
                  className="px-4 py-3 rounded-lg bg-input border border-border text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                  onKeyPress={(e) => e.key === 'Enter' && handleJoinGame()}
                />
                <Button variant="civilian" onClick={handleJoinGame}>
                  Join
                </Button>
                <Button variant="ghost" onClick={() => setShowJoinForm(false)}>
                  Cancel
                </Button>
              </div>
            )}

            <Button
              size="lg"
              variant="outline"
              className="text-lg px-8 py-6 h-auto"
              onClick={() => setShowPublicGames(true)}
            >
              <Globe className="w-5 h-5" />
              Browse Public Games
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="game-card text-center">
              <CardHeader>
                <div className="flex justify-center text-primary mb-2">
                  {feature.icon}
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Game Rules Summary */}
        <Card className="game-card">
          <CardHeader>
            <CardTitle className="text-2xl text-center text-glow-secondary">How to Play</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="space-y-2">
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mx-auto">
                  <span className="text-primary font-bold text-xl">1</span>
                </div>
                <h3 className="font-semibold">Get Your Role</h3>
                <p className="text-sm text-muted-foreground">
                  Receive your secret role: Mafia or Civilian
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center mx-auto">
                  <span className="text-accent font-bold text-xl">2</span>
                </div>
                <h3 className="font-semibold">Discuss & Vote</h3>
                <p className="text-sm text-muted-foreground">
                  Talk with others and vote to eliminate suspects
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 rounded-full bg-civilian/20 flex items-center justify-center mx-auto">
                  <span className="text-civilian font-bold text-xl">3</span>
                </div>
                <h3 className="font-semibold">Win the Game</h3>
                <p className="text-sm text-muted-foreground">
                  Eliminate all mafia as civilians, or survive as mafia
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Testing Components - Remove in production */}
        <div className="space-y-6">
          {/* User Management - Most Important for Testing */}
          <UserManager />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <DatabaseTest />
            <DatabaseFixer />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SimpleGameCreator />
            <MockGameCreator />
          </div>
          <SimpleMultiTabTester />
          <MultiTabTester />
          <BrowserTester />
          <MultiPlayerTester />
          <MockGameDebugger />
        </div>
      </div>

      {/* Public Games Dialog */}
      <Dialog open={showPublicGames} onOpenChange={setShowPublicGames}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <PublicGameBrowser onClose={() => setShowPublicGames(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GameLanding;