import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useGame } from '@/hooks/useGame';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Copy, Share2, Crown, Users, Clock, Zap, X, UserPlus } from 'lucide-react';
import RealTimeStatus from './RealTimeStatus';

const WaitingRoom = () => {
  const navigate = useNavigate();
  const { gameId } = useParams<{ gameId: string }>();
  const { user } = useAuth();
  const { toast } = useToast();
  const { 
    game, 
    players, 
    currentPlayer, 
    loading, 
    joinGame, 
    toggleReady, 
    startGame 
  } = useGame(gameId || '');
  
  const [timeInRoom, setTimeInRoom] = useState(0);
  const [joinName, setJoinName] = useState('');
  const [showJoinDialog, setShowJoinDialog] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeInRoom(prev => prev + 1);
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (game && user && !currentPlayer) {
      setShowJoinDialog(true);
    }
  }, [game, user, currentPlayer]);

  // Check if this is a mock game
  const isMockGame = gameId ? localStorage.getItem(`mock_game_${gameId}`) !== null : false;

  // Real-time update indicator
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());

  useEffect(() => {
    if (!isMockGame) return;

    const updateIndicator = () => {
      setLastUpdateTime(Date.now());
    };

    const interval = setInterval(updateIndicator, 1000);
    return () => clearInterval(interval);
  }, [isMockGame]);

  useEffect(() => {
    if (game && game.status !== 'waiting') {
      navigate(`/game/${gameId}`);
    }
  }, [game, gameId, navigate]);

  const copyGameId = () => {
    if (gameId) {
      navigator.clipboard.writeText(gameId);
      toast({
        title: "Copied!",
        description: "Game ID copied to clipboard",
      });
    }
  };

  const handleJoinGame = async () => {
    if (!joinName.trim()) return;
    await joinGame(joinName);
    setShowJoinDialog(false);
  };

  const handleStartGame = async () => {
    const readyPlayers = players.filter(p => p.is_ready);
    if (readyPlayers.length >= 3) {
      await startGame();
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-muted to-background flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!game) {
    console.error('Game not found for ID:', gameId);
    console.log('Checking localStorage for mock game...');
    const mockGameExists = localStorage.getItem(`mock_game_${gameId}`) !== null;
    console.log('Mock game exists:', mockGameExists);

    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-muted to-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold text-destructive">Game Not Found</h2>
          <p className="text-muted-foreground">Game ID: {gameId}</p>
          <p className="text-muted-foreground">Mock game in localStorage: {mockGameExists ? 'Yes' : 'No'}</p>
          <Button onClick={() => window.location.href = '/'}>
            Return to Home
          </Button>
        </div>
      </div>
    );
  }

  const readyPlayers = players.filter(p => p.is_ready);
  const isHost = currentPlayer && game.host_id === currentPlayer.user_id;
  const emptySlots = Array.from({ length: game.max_players - players.length }, (_, i) => i);

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted to-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        
        <Card className="glass-effect border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Crown className="w-6 h-6 text-amber-500" />
                <span>Game Room</span>
              </div>
              <div className="flex items-center gap-2">
                {isMockGame && (
                  <div className="flex items-center gap-1">
                    <Badge variant="secondary" className="text-xs">
                      MOCK
                    </Badge>
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Real-time updates active"></div>
                  </div>
                )}
                <Badge variant="outline" className="text-lg px-3 py-1">
                  {gameId}
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex gap-3">
              <Button onClick={copyGameId} variant="outline" className="flex-1">
                <Copy className="w-4 h-4 mr-2" />
                Copy ID
              </Button>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold text-lg flex items-center gap-2">
                <Users className="w-5 h-5" />
                Players ({players.length}/{game.max_players})
              </h3>
              
              <div className="grid gap-3">
                {players.map((player) => (
                  <div
                    key={player.id}
                    className="flex items-center justify-between p-3 rounded-lg bg-card border"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarFallback>
                          {player.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {player.name}
                            {player.user_id === user?.id && " (You)"}
                          </span>
                          {player.user_id === game.host_id && (
                            <Crown className="w-4 h-4 text-amber-500" />
                          )}
                        </div>
                        <Badge 
                          variant={player.is_ready ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {player.is_ready ? "Ready" : "Not Ready"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}

                {emptySlots.map((slot) => (
                  <div
                    key={`empty-${slot}`}
                    className="flex items-center p-3 rounded-lg bg-muted/50 border-dashed border-2"
                  >
                    <div className="flex items-center gap-3 opacity-60">
                      <Avatar>
                        <AvatarFallback>?</AvatarFallback>
                      </Avatar>
                      <span className="text-muted-foreground">Waiting for player...</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />
            
            {currentPlayer && (
              <div className="space-y-3">
                <Button 
                  onClick={toggleReady}
                  variant={currentPlayer.is_ready ? "secondary" : "default"}
                  className="w-full"
                >
                  {currentPlayer.is_ready ? "Mark Not Ready" : "Mark Ready"}
                </Button>
                
                {isHost && (
                  <Button
                    onClick={handleStartGame}
                    disabled={readyPlayers.length < 3}
                    className="w-full"
                    size="lg"
                  >
                    <Zap className="w-5 h-5 mr-2" />
                    Start Game ({readyPlayers.length}/3+ ready)
                  </Button>
                )}
                
                {readyPlayers.length < 3 && (
                  <p className="text-sm text-muted-foreground text-center">
                    Need at least 3 ready players to start the game
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Real-time Status for Mock Games */}
        {isMockGame && (
          <RealTimeStatus gameId={gameId} className="mt-4" />
        )}
      </div>

      <Dialog open={showJoinDialog} onOpenChange={setShowJoinDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Join Game</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="join-name">Your Name</Label>
              <Input
                id="join-name"
                placeholder="Enter your name"
                value={joinName}
                onChange={(e) => setJoinName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' ? handleJoinGame() : null}
              />
            </div>
            <Button 
              onClick={handleJoinGame}
              disabled={!joinName.trim()}
              className="w-full"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Join Game
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WaitingRoom;